import React, { useState } from 'react';
import { Setting<PERSON>, Monitor } from 'lucide-react';
import { ConfigProvider } from './contexts/ConfigContext';
import Configuration from './components/Configuration';
import LogsDashboard from './components/LogsDashboard';
import ErrorBoundary from './components/ErrorBoundary';
import { ToastProvider } from './components/Toast';
import './App.css';

function App() {
  const [isConfigOpen, setIsConfigOpen] = useState(false);

  return (
    <ErrorBoundary>
      <ToastProvider>
        <ConfigProvider>
          <div className="h-screen flex flex-col bg-gray-100">
            {/* Header */}
            <header className="bg-white shadow-sm border-b border-gray-200">
              <div className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Monitor className="w-8 h-8 text-blue-600" />
                    <div>
                      <h1 className="text-xl font-semibold text-gray-900">
                        Render Logs Dashboard
                      </h1>
                      <p className="text-sm text-gray-600">
                        Monitor your Render services in real-time
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsConfigOpen(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <Settings className="w-4 h-4" />
                    Settings
                  </button>
                </div>
              </div>
            </header>

            {/* Main Content */}
            <main className="flex-1 overflow-hidden">
              <LogsDashboard />
            </main>

            {/* Configuration Modal */}
            <Configuration
              isOpen={isConfigOpen}
              onClose={() => setIsConfigOpen(false)}
            />
          </div>
        </ConfigProvider>
      </ToastProvider>
    </ErrorBoundary>
  );
}

export default App;
