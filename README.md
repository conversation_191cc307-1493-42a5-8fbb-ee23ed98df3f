# Shopify Return Automation System

A comprehensive webhook-based return/warranty system that integrates Shopify with multiple suppliers, email notifications, and logging services.

## Features

- **Webhook Processing**: Handles Return Prime webhook events (request.created, request.approved, etc.)
- **Supplier Integration**: Automatically maps products to suppliers (SmokeDrop, Buddify, Canna River, Discreet Smoker)
- **Email Notifications**: Sends automated emails to suppliers with return details
- **Shopify Integration**: Creates returns/exchanges in Shopify via GraphQL API
- **Real-time Logging**: Tracks all return requests in Google Sheets with live dashboard monitoring
- **Client Dashboard**: Beautiful real-time dashboard for monitoring return processing
- **Error Handling**: Robust retry mechanisms and in-memory idempotency checks
- **Security**: Webhook signature verification and input validation
- **CORS**: Allows access from all origins for maximum flexibility

## Architecture

```
Return Prime Webhook → Express Server → Supplier Mapping → Email Notifications
                                    ↓                            ↓
                              Shopify GraphQL ← Exchange Processing
                                    ↓                            ↓
                              Google Sheets ← Real-time Dashboard ← Live Monitoring
```

## Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository>
   cd shopify-return-automation
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Build and Run**
   ```bash
   npm run build
   npm start
   
   # For development
   npm run dev
   ```

4. **Access Dashboards**
   ```bash
   # Admin dashboard (supplier management)
   http://localhost:3000/dashboard

   # Client logs dashboard (real-time monitoring)
   http://localhost:3000/logs
   ```

## Dashboards

### Admin Dashboard (`/dashboard`)
- Supplier management and configuration
- Email template testing
- System health monitoring
- API connection testing

### Client Logs Dashboard (`/logs`)
- **Real-time monitoring** of return processing
- **Live statistics** (processed returns, notifications sent, errors)
- **Interactive log viewer** with filtering
- **Google Sheets export** functionality
- **Auto-refresh** with connection status
- **Beautiful UI** designed for client visibility

## Configuration

### Required Environment Variables

```env
# Server
PORT=3000
NODE_ENV=production

# Webhook Security
RETURN_PRIME_WEBHOOK_SECRET=your_webhook_secret

# Shopify
SHOPIFY_STORE_URL=bakebuds.myshopify.com
SHOPIFY_ACCESS_TOKEN=your_access_token

# Email Configuration (Nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# In-memory idempotency (no external dependencies)
```

### Optional Services

**Google Sheets Logging (Recommended):**
```env
GOOGLE_SHEETS_ID=your_sheets_id
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
```

> 📋 **Google Sheets Setup**: See [GOOGLE_SHEETS_SETUP.md](./GOOGLE_SHEETS_SETUP.md) for detailed setup instructions.
>
> 🧪 **Test Integration**: Run `node test-google-sheets.js` to verify your setup.

**Airtable Logging:**
```env
AIRTABLE_API_KEY=your_api_key
AIRTABLE_BASE_ID=your_base_id
AIRTABLE_TABLE_NAME=Returns
```

## Webhook Setup

### Return Prime Webhook Configuration

1. In your Return Prime dashboard, go to Webhooks
2. Set the destination URL to: `https://your-domain.com/webhook/return-prime`
3. Select these events:
   - ✅ Request approved
   - ✅ Request updated
   - ✅ Request received
   - ✅ Request inspected
   - ✅ Request refunded
   - ✅ Request rejected
   - ✅ Request archived
   - ✅ Request created
   - ✅ Request refund failed

### Webhook Payload Example

```json
{
  "event_type": "request.created",
  "return_id": "RET-12345",
  "order_id": "1234567890",
  "customer_email": "<EMAIL>",
  "customer_name": "John Doe",
  "items": [
    {
      "sku": "BUDDIFY-PRODUCT-001",
      "name": "Sample Product",
      "qty": 1,
      "reason": "Defective",
      "images": ["https://example.com/image1.jpg"],
      "vendor_name": "Buddify"
    }
  ],
  "created_at": "2023-12-01T10:00:00Z",
  "status": "pending",
  "exchange": false
}
```

## Supplier Mapping

The system uses a **two-step process** to determine which supplier handles each return:

### Step 1: Vendor Enrichment from Shopify
Since Return Prime webhooks don't include vendor information, the system:

1. **Extracts product/variant IDs** from Return Prime webhook
2. **Queries Shopify API** to get vendor information:
   ```
   GET /admin/api/2023-07/products/{product_id}.json
   GET /admin/api/2023-07/variants/{variant_id}.json
   ```
3. **Enriches return items** with actual vendor data from Shopify

### Step 2: Supplier Mapping
Products are then mapped to suppliers using:

1. **Vendor Names (Primary - from Shopify):**
   - Products with vendor "Buddify" → Buddify
   - Products with vendor "SmokeDrop" → SmokeDrop
   - Products with vendor "Canna River" → Canna River
   - Products with vendor "Discreet Smoker" → Discreet Smoker

2. **SKU Prefixes (Fallback):**
   - `BUDDIFY-*` → Buddify
   - `SMOKEDROP-*`, `SD-*` → SmokeDrop
   - `CANNA-*`, `CR-*`, `CANNARIVER-*` → Canna River
   - `DISCREET-*`, `DS-*` → Discreet Smoker

3. **Unknown Handling:**
   - Products that don't match any vendor or SKU pattern are sent to internal review team

## API Endpoints

- `GET /` - Service status
- `POST /webhook/return-prime` - Main webhook endpoint
- `GET /health` - Health check
- `GET /config/validate` - Configuration validation

## Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Lint code
npm run lint
npm run lint:fix
```

## Deployment

### Docker (Recommended)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

### Environment Variables for Production

Ensure all required environment variables are set in your production environment. Use a service like AWS Parameter Store, Azure Key Vault, or similar for sensitive values.

## Monitoring

The application logs to:
- Console (development)
- Files: `logs/error.log`, `logs/combined.log`
- External logging service (if configured)

Key metrics to monitor:
- Webhook processing success rate
- Email delivery success rate
- Shopify API response times
- Redis connection status

## Troubleshooting

### Common Issues

1. **Webhook signature verification fails**
   - Check `RETURN_PRIME_WEBHOOK_SECRET` matches Return Prime configuration
   - Ensure webhook payload is not modified by proxies

2. **Email sending fails**
   - Verify SMTP credentials (EMAIL_USER and EMAIL_PASS)
   - Check if Gmail App Password is correctly set
   - Ensure SMTP settings match your email provider

3. **Shopify API errors**
   - Verify access token has required permissions
   - Check API rate limits
   - Ensure store URL is correct

4. **In-memory storage limitations**
   - Note that idempotency is lost on server restart
   - Consider implementing persistent storage for production
   - Monitor memory usage for high-volume scenarios

### Configuration Validation

Run the configuration validator:
```bash
curl http://localhost:3000/config/validate
```

## Security

- Webhook signature verification prevents unauthorized requests
- Input validation using Joi schemas
- Rate limiting and CORS protection
- Secure handling of sensitive data
- Idempotency prevents duplicate processing

## License

MIT License - see LICENSE file for details
