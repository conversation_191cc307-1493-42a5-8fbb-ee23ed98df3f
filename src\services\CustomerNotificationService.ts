import nodemailer from 'nodemailer';
import { config } from '../config';
import logger from '../utils/logger';
import { ReturnItem } from '../types';

export interface CustomerNotificationData {
  returnId: string;
  orderId: string;
  orderNumber?: string;
  customerEmail: string;
  customerName?: string;
  items: ReturnItem[];
  requestType: 'return' | 'warranty' | 'exchange';
  submittedDate: string;
  estimatedProcessingTime?: string;
  trackingInfo?: {
    returnLabel?: string;
    trackingNumber?: string;
    carrierName?: string;
  };
  notes?: string;
}

export class CustomerNotificationService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.port === 465, // true for 465, false for other ports
      auth: {
        user: config.email.user,
        pass: config.email.pass
      },
      tls: {
        rejectUnauthorized: false
      }
    });
  }

  /**
   * Send return/warranty processing confirmation to customer
   */
  public async notifyCustomer(
    notificationData: CustomerNotificationData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Generate beautiful email template
      const template = this.generateCustomerEmailTemplate(notificationData);
      
      // Send email to actual customer from webhook data
      const mailOptions = {
        from: config.email.from,
        to: notificationData.customerEmail, // Send to actual customer email from webhook
        subject: template.subject,
        html: template.html,
        text: template.text,
        replyTo: config.email.from
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Also send a copy to support for tracking
      const supportCopyOptions = {
        from: config.email.from,
        to: ['<EMAIL>'],
        subject: `[CUSTOMER COPY] ${template.subject} - ${notificationData.customerName || notificationData.customerEmail}`,
        html: `
          <div style="background-color: #f0fff0; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="color: #009900; margin: 0;">👤 CUSTOMER NOTIFICATION COPY</h3>
            <p style="margin: 10px 0 0 0; color: #666;">
              This is a copy of the notification sent to customer: <strong>${notificationData.customerEmail}</strong>
            </p>
          </div>
          ${template.html}
        `,
        text: `[CUSTOMER COPY] - This is a copy of the notification sent to customer: ${notificationData.customerEmail}\n\n${template.text}`,
        replyTo: config.email.from
      };

      // Send support copy (don't wait for it)
      this.transporter.sendMail(supportCopyOptions).catch(error => {
        logger.error('Failed to send customer notification copy to support', {
          error: error.message,
          customerEmail: notificationData.customerEmail,
          returnId: notificationData.returnId
        });
      });
      
      logger.info('Customer notification sent successfully', {
        customerEmail: notificationData.customerEmail,
        returnId: notificationData.returnId,
        requestType: notificationData.requestType,
        messageId: result.messageId
      });

      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error: any) {
      logger.error('Failed to send customer notification', {
        customerEmail: notificationData.customerEmail,
        returnId: notificationData.returnId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate beautiful 3D-style email template for customer
   */
  public generateCustomerEmailTemplate(data: CustomerNotificationData): {
    subject: string;
    html: string;
    text: string;
  } {
    const requestTypeDisplay = data.requestType.charAt(0).toUpperCase() + data.requestType.slice(1);
    const subject = `✅ Your ${requestTypeDisplay} Request #${data.returnId} is Being Processed`;
    
    // Generate simple items list
    const itemsHtml = data.items.map(item => `
      <div class="info-card">
        <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #333333;">
          ${item.name}
        </h4>
        <p style="margin: 4px 0; color: #666666;">
          <strong>SKU:</strong> ${item.sku}
        </p>
        <p style="margin: 4px 0; color: #666666;">
          <strong>Quantity:</strong> ${item.qty}
        </p>
        <p style="margin: 4px 0; color: #666666;">
          <strong>Reason:</strong> ${item.reason}
        </p>
      </div>
    `).join('');

    const itemsText = data.items.map(item => 
      `• ${item.name} (SKU: ${item.sku}) - Qty: ${item.qty} - Reason: ${item.reason}`
    ).join('\n');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${requestTypeDisplay} Request Confirmation</title>
        <style>
          * {
            box-sizing: border-box;
          }

          body {
            margin: 0;
            padding: 20px;
            background: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333333;
          }

          @media (max-width: 600px) {
            body {
              padding: 15px;
            }
          }

          @media (prefers-color-scheme: dark) {
            body {
              background: #000000 !important;
              color: #ffffff !important;
            }
          }
          
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 0;
          }

          @media (max-width: 600px) {
            .container {
              margin: 0;
              max-width: 100%;
              border-radius: 4px;
              border: none;
            }
          }

          @media (prefers-color-scheme: dark) {
            .container {
              background: #111111 !important;
              border: 1px solid #333333 !important;
              color: #ffffff !important;
            }
          }
          
          .header {
            background: #f8f9fa;
            padding: 24px;
            border-bottom: 1px solid #e5e5e5;
            border-radius: 8px 8px 0 0;
          }

          @media (max-width: 600px) {
            .header {
              padding: 20px;
              border-radius: 4px 4px 0 0;
            }
          }

          @media (prefers-color-scheme: dark) {
            .header {
              background: #1a1a1a !important;
              border-bottom: 1px solid #333333 !important;
            }
          }

          .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #333333;
          }

          @media (max-width: 600px) {
            .header h1 {
              font-size: 18px;
            }
          }

          @media (prefers-color-scheme: dark) {
            .header h1 {
              color: #ffffff !important;
            }
          }
          
          .status-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            position: relative;
            z-index: 1;
          }
          
          .content {
            padding: 24px;
            background: #ffffff;
          }

          @media (max-width: 600px) {
            .content {
              padding: 20px;
            }
          }

          @media (prefers-color-scheme: dark) {
            .content {
              background: #111111 !important;
              color: #ffffff !important;
            }
          }
          
          .info-card {
            background: #f8f9fa;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            padding: 16px;
            margin: 16px 0;
          }

          @media (max-width: 600px) {
            .info-card {
              padding: 14px;
              margin: 12px 0;
            }
          }

          @media (prefers-color-scheme: dark) {
            .info-card {
              background: #1a1a1a !important;
              border: 1px solid #333333 !important;
              color: #ffffff !important;
            }
          }
          
          h2, h3, h4 {
            margin: 0 0 12px 0;
            color: #333333;
            font-weight: 600;
          }

          @media (prefers-color-scheme: dark) {
            h2, h3, h4 {
              color: #ffffff !important;
            }
          }

          p {
            margin: 0 0 8px 0;
            color: #666666;
            line-height: 1.5;
          }

          @media (prefers-color-scheme: dark) {
            p {
              color: #cccccc !important;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${requestTypeDisplay} Request Received</h1>
          </div>

          <div class="content">
            <p><strong>Hi ${data.customerName || 'Valued Customer'},</strong></p>
            <p>We've received your ${data.requestType} request and our team is processing it.</p>

            <div class="info-card">
              <h3 style="margin-top: 0;">Request Details</h3>
              <p><strong>Request ID:</strong> #${data.returnId}</p>
              <p><strong>Order Number:</strong> #${data.orderNumber || data.orderId}</p>
              <p><strong>Request Type:</strong> ${requestTypeDisplay}</p>
              <p><strong>Submitted:</strong> ${new Date(data.submittedDate).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}</p>
              ${data.estimatedProcessingTime ? `
              <p><strong>Estimated Processing:</strong> ${data.estimatedProcessingTime}</p>
              ` : ''}
            </div>

            <h3>Items in Your Request</h3>
            ${itemsHtml}

            <h3>What Happens Next</h3>
            <p>1. <strong>Request Received</strong> - We've got your ${data.requestType} request</p>
            <p>2. <strong>Supplier Notified</strong> - We've contacted the relevant suppliers</p>
            <p>3. <strong>Review & Processing</strong> - Suppliers will review your request</p>
            <p>4. <strong>Resolution</strong> - You'll receive updates on the outcome</p>

            ${data.trackingInfo ? `
            <div class="info-card">
              <h3>Shipping Information</h3>
              ${data.trackingInfo.returnLabel ? `
              <p><strong>Return Label:</strong> ${data.trackingInfo.returnLabel}</p>
              ` : ''}
              ${data.trackingInfo.trackingNumber ? `
              <p><strong>Tracking Number:</strong> ${data.trackingInfo.trackingNumber}</p>
              ` : ''}
              ${data.trackingInfo.carrierName ? `
              <p><strong>Carrier:</strong> ${data.trackingInfo.carrierName}</p>
              ` : ''}
            </div>
            ` : ''}

            ${data.notes ? `
            <div class="info-card">
              <h3>Additional Notes</h3>
              <p>${data.notes}</p>
            </div>
            ` : ''}

            <p>If you have any questions about your ${data.requestType} request, feel free to reach out to our support team at ${config.email.from}.</p>

            <p>Thank you for choosing Bake Buds!</p>

            <hr style="border: none; border-top: 1px solid #e5e5e5; margin: 24px 0;">

            <p style="font-size: 14px; color: #666666;">
              This is an automated message from Bake Buds Return Processing System.<br>
              If you have questions, reply to this email or contact ${config.email.from}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
🎉 ${requestTypeDisplay} Request Received!

Hi ${data.customerName || 'Valued Customer'}!

Great news! We've received your ${data.requestType} request and our team is already working on it.

REQUEST DETAILS:
- Request ID: #${data.returnId}
- Order Number: #${data.orderNumber || data.orderId}
- Request Type: ${requestTypeDisplay}
- Submitted: ${new Date(data.submittedDate).toLocaleDateString()}
${data.estimatedProcessingTime ? `- Estimated Processing: ${data.estimatedProcessingTime}` : ''}

ITEMS IN YOUR REQUEST:
${itemsText}

WHAT HAPPENS NEXT:
✅ Request Received - We've got your ${data.requestType} request!
✅ Supplier Notified - We've contacted the relevant suppliers
⏳ Review & Processing - Suppliers will review your request
⏳ Resolution - You'll receive updates on the outcome

${data.trackingInfo ? `
SHIPPING INFORMATION:
${data.trackingInfo.returnLabel ? `- Return Label: ${data.trackingInfo.returnLabel}` : ''}
${data.trackingInfo.trackingNumber ? `- Tracking Number: ${data.trackingInfo.trackingNumber}` : ''}
${data.trackingInfo.carrierName ? `- Carrier: ${data.trackingInfo.carrierName}` : ''}
` : ''}

${data.notes ? `
ADDITIONAL NOTES:
${data.notes}
` : ''}

NEED HELP?
If you have any questions about your ${data.requestType} request, 
feel free to reach out to our support team at ${config.email.from}

Thank you for choosing Bake Buds!
We appreciate your business and are committed to providing excellent service.

This is an automated message from Bake Buds Return Processing System.
    `;

    return {
      subject,
      html,
      text
    };
  }

  /**
   * Test email configuration
   */
  public async testEmailConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Customer email connection test successful');
      return true;
    } catch (error: any) {
      logger.error('Customer email connection test failed', { error: error.message });
      return false;
    }
  }
}
