import mongoose, { Document, Schema } from 'mongoose';

export interface ISupplierContact {
  email: string;
  phone?: string;
  website?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  businessHours?: string;
  timezone?: string;
}

export interface ISupplierReturnPolicy {
  url?: string;
  timeLimit?: string;
  requirements?: string;
  restockingFee?: string;
  returnAddress?: string;
}

export interface ISupplierProducts {
  count?: number;
  categories?: string[];
  tags?: string[];
}

export interface ISupplierAutomation {
  emailTemplate?: string;
  requiresApproval: boolean;
  autoRefund: boolean;
  processingTime: string;
}

export interface ISupplier extends Document {
  name: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  verified: boolean;
  active: boolean;
  contact: ISupplierContact;
  returnPolicy?: ISupplierReturnPolicy;
  products?: ISupplierProducts;
  automation: ISupplierAutomation;
  notes?: string;
  tags: string[];
  vendorNames: string[]; // Alternative names this supplier might be known by
  shopifyVendorNames: string[]; // Vendor names as they appear in Shopify
  createdAt: Date;
  updatedAt: Date;
}

const SupplierContactSchema = new Schema<ISupplierContact>({
  email: { type: String, required: true },
  phone: { type: String },
  website: { type: String },
  address: {
    street: { type: String },
    city: { type: String },
    state: { type: String },
    zip: { type: String },
    country: { type: String }
  },
  businessHours: { type: String },
  timezone: { type: String }
});

const SupplierReturnPolicySchema = new Schema<ISupplierReturnPolicy>({
  url: { type: String },
  timeLimit: { type: String },
  requirements: { type: String },
  restockingFee: { type: String },
  returnAddress: { type: String }
});

const SupplierProductsSchema = new Schema<ISupplierProducts>({
  count: { type: Number },
  categories: [{ type: String }],
  tags: [{ type: String }]
});

const SupplierAutomationSchema = new Schema<ISupplierAutomation>({
  emailTemplate: { type: String },
  requiresApproval: { type: Boolean, required: true, default: true },
  autoRefund: { type: Boolean, required: true, default: false },
  processingTime: { type: String, required: true, default: '2-3 business days' }
});

const SupplierSchema = new Schema<ISupplier>({
  name: { 
    type: String, 
    required: true, 
    unique: true,
    trim: true,
    index: true
  },
  priority: { 
    type: String, 
    enum: ['HIGH', 'MEDIUM', 'LOW'], 
    required: true,
    default: 'MEDIUM',
    index: true
  },
  verified: { 
    type: Boolean, 
    required: true, 
    default: false,
    index: true
  },
  active: { 
    type: Boolean, 
    required: true, 
    default: true,
    index: true
  },
  contact: { 
    type: SupplierContactSchema, 
    required: true 
  },
  returnPolicy: SupplierReturnPolicySchema,
  products: SupplierProductsSchema,
  automation: { 
    type: SupplierAutomationSchema, 
    required: true 
  },
  notes: { type: String },
  tags: [{ 
    type: String, 
    index: true 
  }],
  vendorNames: [{ 
    type: String, 
    index: true 
  }],
  shopifyVendorNames: [{ 
    type: String, 
    index: true 
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient querying
SupplierSchema.index({ name: 'text', 'contact.email': 'text' });
SupplierSchema.index({ tags: 1 });
SupplierSchema.index({ vendorNames: 1 });
SupplierSchema.index({ shopifyVendorNames: 1 });
SupplierSchema.index({ priority: 1, verified: 1, active: 1 });

// Virtual for full text search
SupplierSchema.virtual('searchText').get(function() {
  return `${this.name} ${this.contact.email} ${this.tags.join(' ')} ${this.vendorNames.join(' ')} ${this.shopifyVendorNames.join(' ')}`;
});

// Define interface for static methods
interface ISupplierModel extends mongoose.Model<ISupplier> {
  findByVendorName(vendorName: string): Promise<ISupplier[]>;
  findByTags(tags: string[]): Promise<ISupplier[]>;
  findActiveSuppliers(): Promise<ISupplier[]>;
}

// Static methods
SupplierSchema.statics.findByVendorName = function(vendorName: string) {
  return this.find({
    $or: [
      { name: new RegExp(vendorName, 'i') },
      { vendorNames: new RegExp(vendorName, 'i') },
      { shopifyVendorNames: new RegExp(vendorName, 'i') }
    ],
    active: true
  });
};

SupplierSchema.statics.findByTags = function(tags: string[]) {
  return this.find({
    tags: { $in: tags },
    active: true
  });
};

SupplierSchema.statics.findActiveSuppliers = function() {
  return this.find({ active: true }).sort({ priority: 1, name: 1 });
};

export const Supplier = mongoose.model<ISupplier, ISupplierModel>('Supplier', SupplierSchema);
