const { createAdminApiClient } = require('@shopify/admin-api-client');
require('dotenv').config();

class VendorMetafieldRemover {
  constructor() {
    this.client = createAdminApiClient({
      storeDomain: process.env.SHOPIFY_STORE_URL,
      accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
      apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
    });
  }

  // Remove all vendor metafields from products
  async removeAllVendorMetafields() {
    console.log('🗑️ Removing all vendor metafields from products...\n');
    
    // Get all products with vendor metafields
    const query = `
      query {
        products(first: 250) {
          edges {
            node {
              id
              title
              vendor
              metafields(namespace: "vendor", first: 20) {
                edges {
                  node {
                    id
                    namespace
                    key
                  }
                }
              }
            }
          }
        }
      }
    `;

    try {
      const response = await this.client.request(query);
      const products = response.data?.products?.edges || [];
      
      let totalRemoved = 0;
      let productsProcessed = 0;

      for (const productEdge of products) {
        const product = productEdge.node;
        const vendorMetafields = product.metafields?.edges || [];
        
        if (vendorMetafields.length > 0) {
          console.log(`📦 Processing: ${product.title}`);
          console.log(`   Vendor: ${product.vendor}`);
          console.log(`   Found ${vendorMetafields.length} vendor metafields`);
          
          // Remove each vendor metafield
          for (const metafieldEdge of vendorMetafields) {
            const metafield = metafieldEdge.node;
            
            try {
              const deleteMutation = `
                mutation metafieldDelete($input: MetafieldDeleteInput!) {
                  metafieldDelete(input: $input) {
                    deletedId
                    userErrors {
                      field
                      message
                    }
                  }
                }
              `;

              const variables = {
                input: {
                  id: metafield.id
                }
              };

              const deleteResponse = await this.client.request(deleteMutation, { variables });
              
              if (deleteResponse.data?.metafieldDelete?.deletedId) {
                console.log(`     ✅ Removed: ${metafield.key}`);
                totalRemoved++;
              } else {
                const errors = deleteResponse.data?.metafieldDelete?.userErrors || [];
                console.log(`     ❌ Failed to remove ${metafield.key}:`, errors);
              }
            } catch (error) {
              console.log(`     ❌ Error removing ${metafield.key}:`, error.message);
            }
          }
          
          productsProcessed++;
          console.log(`   ✅ Completed ${product.title}\n`);
        }
      }
      
      console.log('🎉 Vendor metafield removal completed!');
      console.log(`📊 Summary:`);
      console.log(`   Products processed: ${productsProcessed}`);
      console.log(`   Metafields removed: ${totalRemoved}`);
      
    } catch (error) {
      console.error('❌ Error removing vendor metafields:', error.message);
      throw error;
    }
  }

  // Remove vendor metafield definitions
  async removeVendorMetafieldDefinitions() {
    console.log('🗑️ Removing vendor metafield definitions...\n');
    
    const query = `
      query {
        metafieldDefinitions(namespace: "vendor", first: 20) {
          edges {
            node {
              id
              name
              namespace
              key
            }
          }
        }
      }
    `;

    try {
      const response = await this.client.request(query);
      const definitions = response.data?.metafieldDefinitions?.edges || [];
      
      console.log(`Found ${definitions.length} vendor metafield definitions to remove`);
      
      for (const definitionEdge of definitions) {
        const definition = definitionEdge.node;
        
        try {
          const deleteMutation = `
            mutation metafieldDefinitionDelete($id: ID!) {
              metafieldDefinitionDelete(id: $id) {
                deletedDefinitionId
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          const variables = { id: definition.id };
          const deleteResponse = await this.client.request(deleteMutation, { variables });
          
          if (deleteResponse.data?.metafieldDefinitionDelete?.deletedDefinitionId) {
            console.log(`✅ Removed definition: ${definition.name} (${definition.namespace}.${definition.key})`);
          } else {
            const errors = deleteResponse.data?.metafieldDefinitionDelete?.userErrors || [];
            console.log(`❌ Failed to remove ${definition.name}:`, errors);
          }
        } catch (error) {
          console.log(`❌ Error removing ${definition.name}:`, error.message);
        }
      }
      
    } catch (error) {
      console.error('❌ Error removing metafield definitions:', error.message);
      throw error;
    }
  }
}

// Main execution
async function main() {
  try {
    const remover = new VendorMetafieldRemover();
    
    console.log('🚀 Starting vendor metafield cleanup...\n');
    
    // Step 1: Remove all vendor metafields from products
    await remover.removeAllVendorMetafields();
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Step 2: Remove vendor metafield definitions
    await remover.removeVendorMetafieldDefinitions();
    
    console.log('\n🎉 All vendor metafields have been removed!');
    console.log('📋 Vendor data is now private and not exposed to customers.');
    
  } catch (error) {
    console.error('❌ Error in vendor metafield cleanup:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = VendorMetafieldRemover;
