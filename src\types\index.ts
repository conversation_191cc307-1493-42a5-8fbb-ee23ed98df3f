// Return Prime Webhook Types (Actual Format)
export interface ReturnPrimeWebhookPayload {
  request: {
    id: string;
    status: 'pending' | 'approved' | 'rejected' | 'processing' | 'completed';
    created_at: string;
    request_number: string;
    request_type: 'return' | 'exchange';
    customer: {
      id: number;
      name: string;
      email: string;
      phone?: string;
      address?: {
        address_line_1: string;
        address_line_2?: string;
        city: string;
        country: string;
        country_code: string;
        province: string;
        province_code: string;
        postal_code: string;
        first_name: string;
        last_name?: string;
      };
    };
    order: {
      id: number;
      name: string;
      created_at: string;
      fulfillments: Array<{
        id: number;
        line_items: number[];
        delivery_date?: string;
        delivery_status?: string;
      }>;
    };
    line_items: ReturnPrimeLineItem[];
    approved: {
      status: boolean;
      created_at?: string;
      comment?: string;
    };
    rejected: {
      status: boolean;
      created_at?: string;
      comment?: string;
    };
    received: {
      status: boolean;
      created_at?: string;
      comment?: string;
    };
    inspected: {
      status: boolean;
      created_at?: string;
      comment?: string;
    };
    archived: {
      status: boolean;
      created_at?: string;
      comment?: string;
    };
    manual_request: boolean;
    smart_exchange: boolean;
  };
}

export interface ReturnPrimeLineItem {
  id: number;
  quantity: number;
  reason: string;
  notes?: string;
  original_product: {
    product_id: number;
    variant_id: number;
    sku: string;
    title: string;
    variant_title?: string;
    price: number;
    image?: {
      src: string;
    };
    product_deleted: boolean;
    variant_deleted: boolean;
  };
  refund: {
    status: 'pending' | 'completed' | 'failed';
    requested_mode: 'refund' | 'store_credit' | 'exchange';
    actual_mode?: string;
    refunded_at?: string;
    comment?: string;
  };
  exchange?: {
    order?: any;
  };
  presentment_price: {
    actual_amount: number;
    currency: string;
    return_quantity: number;
    shipping_amount: number;
    total_discount: number;
    total_tax: number;
  };
}

export interface ReturnItem {
  sku: string;
  name: string;
  qty: number;
  reason: string;
  images?: string[];
  vendor_name?: string;
  product_id?: string;
  variant_id?: string;
  line_item_id?: string;
}

// Transformed payload interface (our internal format)
export interface TransformedReturnPayload {
  event_type: string;
  return_id: string;
  order_id: string;
  order_number?: string;
  customer_email: string;
  customer_name?: string;
  customer_address?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  items: ReturnItem[];
  created_at: string;
  status: string;
  exchange?: boolean;
  refund_amount?: number;
  refund_method?: string;
  notes?: string;
}

// Supplier Types
export type SupplierName = 'GRAV®' | 'SmokeDrop' | 'Buddify' | 'Canna River' | 'Discreet Smoker' | 'Vessel' | 'Unknown';

export interface SupplierConfig {
  name: SupplierName;
  email: string;
  requiresManualReview: boolean;
  skuPrefixes: string[];
  vendorNames: string[];
}

export interface SupplierNotification {
  supplier: SupplierName;
  email: string;
  items: ReturnItem[];
  returnId: string;
  orderId: string;
  orderNumber?: string;
  customerEmail: string;
  customerName?: string;
  createdAt: string;
  requiresManualReview: boolean;
  // Additional webhook data fields
  estimatedProcessingTime?: string;
  trackingInfo?: {
    returnLabel?: string;
    trackingNumber?: string;
    carrierName?: string;
  };
  customerNotes?: string;
  refundAmount?: string;
  refundMethod?: string;
  customerAddress?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  trackingNumber?: string;
  carrier?: string;
}

// Email Types
export interface EmailTemplate {
  subject: string;
  body: string;
}

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  attachments?: Array<{
    filename: string;
    content: Buffer;
    contentType: string;
  }>;
}

// Logging Types
export interface ReturnLogEntry {
  timestamp: string;
  return_id: string;
  order_id: string;
  sku: string;
  supplier: string; // Changed from SupplierName to string to support dynamic vendors
  qty: number;
  status: string;
  customer_email: string;
  reason: string;
  event_type: string;
  processed_at: string;
  error?: string;
}

// Shopify Types
export interface ShopifyReturnRequest {
  orderId: string;
  lineItems: Array<{
    lineItemId: string;
    quantity: number;
    reason: string;
  }>;
  notifyCustomer: boolean;
  note?: string;
}

// Configuration Types
export interface AppConfig {
  port: number;
  nodeEnv: string;
  webhookSecret: string;
  mongodb?: { // Made optional since we're using vendor database JSON instead
    uri: string;
    dbName: string;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  returnPrime: {
    apiUrl: string;
    adminAccessToken: string;
  };
  shopify: {
    storeUrl: string;
    accessToken: string;
    apiVersion: string;
    webhookSecret: string;
  };
  email: {
    host: string;
    port: number;
    secure: boolean;
    user: string;
    pass: string;
    from: string;
  };
  googleSheets?: {
    sheetsId: string;
    serviceAccountEmail: string;
    privateKey: string;
  };
  airtable?: {
    apiKey: string;
    baseId: string;
    tableName: string;
  };

  suppliers: SupplierConfig[];
  maxRetryAttempts: number;
  retryDelayMs: number;
  internalReviewEmail: string;
}

// Error Types
export interface ProcessingError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
}

// Response Types
export interface WebhookResponse {
  success: boolean;
  message: string;
  processedItems?: number;
  errors?: ProcessingError[];
}
