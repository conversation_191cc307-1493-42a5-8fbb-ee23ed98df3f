import { createAdminApiClient } from '@shopify/admin-api-client';
import { config } from '../config';
import logger from '../utils/logger';

export interface WebhookSubscription {
  id: string;
  topic: string;
  address: string;
  format: string;
  created_at: string;
  updated_at: string;
}

export class ShopifyWebhookService {
  private client: any;

  constructor() {
    this.client = createAdminApiClient({
      storeDomain: config.shopify.storeUrl,
      accessToken: config.shopify.accessToken,
      apiVersion: config.shopify.apiVersion
    });
  }

  /**
   * Create webhook subscriptions for product and order events
   */
  public async setupWebhooks(webhookEndpoint: string): Promise<{
    success: boolean;
    subscriptions?: WebhookSubscription[];
    errors?: string[];
  }> {
    const errors: string[] = [];
    const subscriptions: WebhookSubscription[] = [];

    // Define webhook topics we want to subscribe to
    const webhookTopics = [
      'products/create',
      'products/update', 
      'orders/create',
      'orders/updated',
      'orders/paid'
    ];

    try {
      for (const topic of webhookTopics) {
        try {
          const subscription = await this.createWebhookSubscription(topic, webhookEndpoint);
          if (subscription) {
            subscriptions.push(subscription);
            logger.info('Created Shopify webhook subscription', {
              topic,
              webhookId: subscription.id,
              endpoint: webhookEndpoint
            });
          }
        } catch (error: any) {
          const errorMsg = `Failed to create webhook for ${topic}: ${error.message}`;
          errors.push(errorMsg);
          logger.error('Failed to create Shopify webhook', {
            topic,
            error: error.message,
            endpoint: webhookEndpoint
          });
        }
      }

      return {
        success: errors.length === 0,
        subscriptions,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error: any) {
      logger.error('Failed to setup Shopify webhooks', {
        error: error.message,
        endpoint: webhookEndpoint
      });
      return {
        success: false,
        errors: [error.message]
      };
    }
  }

  /**
   * Create a single webhook subscription
   */
  private async createWebhookSubscription(topic: string, endpoint: string): Promise<WebhookSubscription | null> {
    const mutation = `
      mutation webhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
        webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
          webhookSubscription {
            id
            callbackUrl
            topic
            format
            createdAt
            updatedAt
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variables = {
      topic: topic.toUpperCase().replace('/', '_'),
      webhookSubscription: {
        callbackUrl: endpoint,
        format: 'JSON'
      }
    };

    const response = await this.client.request(mutation, { variables });

    if (response.errors) {
      throw new Error(response.errors.message || 'GraphQL error creating webhook');
    }

    const result = response.data?.webhookSubscriptionCreate;
    if (result?.userErrors?.length > 0) {
      throw new Error(result.userErrors[0].message);
    }

    if (result?.webhookSubscription) {
      return {
        id: result.webhookSubscription.id,
        topic: result.webhookSubscription.topic,
        address: result.webhookSubscription.callbackUrl,
        format: result.webhookSubscription.format,
        created_at: result.webhookSubscription.createdAt,
        updated_at: result.webhookSubscription.updatedAt
      };
    }

    return null;
  }

  /**
   * List existing webhook subscriptions
   */
  public async listWebhooks(): Promise<{
    success: boolean;
    subscriptions?: WebhookSubscription[];
    error?: string;
  }> {
    try {
      const query = `
        query {
          webhookSubscriptions(first: 50) {
            edges {
              node {
                id
                callbackUrl
                topic
                format
                createdAt
                updatedAt
              }
            }
          }
        }
      `;

      const response = await this.client.request(query);

      if (response.errors) {
        return {
          success: false,
          error: response.errors.message || 'GraphQL error'
        };
      }

      const subscriptions = response.data?.webhookSubscriptions?.edges?.map((edge: any) => ({
        id: edge.node.id,
        topic: edge.node.topic,
        address: edge.node.callbackUrl,
        format: edge.node.format,
        created_at: edge.node.createdAt,
        updated_at: edge.node.updatedAt
      })) || [];

      logger.info('Retrieved Shopify webhook subscriptions', {
        count: subscriptions.length
      });

      return {
        success: true,
        subscriptions
      };
    } catch (error: any) {
      logger.error('Failed to list Shopify webhooks', {
        error: error.message
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete a webhook subscription
   */
  public async deleteWebhook(webhookId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const mutation = `
        mutation webhookSubscriptionDelete($id: ID!) {
          webhookSubscriptionDelete(id: $id) {
            deletedWebhookSubscriptionId
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        id: webhookId
      };

      const response = await this.client.request(mutation, { variables });

      if (response.errors) {
        return {
          success: false,
          error: response.errors.message || 'GraphQL error'
        };
      }

      const result = response.data?.webhookSubscriptionDelete;
      if (result?.userErrors?.length > 0) {
        return {
          success: false,
          error: result.userErrors[0].message
        };
      }

      logger.info('Deleted Shopify webhook subscription', {
        webhookId,
        deletedId: result?.deletedWebhookSubscriptionId
      });

      return {
        success: true
      };
    } catch (error: any) {
      logger.error('Failed to delete Shopify webhook', {
        error: error.message,
        webhookId
      });
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Verify webhook signature
   */
  public verifyWebhook(rawBody: string, signature: string): boolean {
    const crypto = require('crypto');
    const hmac = crypto.createHmac('sha256', config.shopify.webhookSecret || '');
    hmac.update(rawBody, 'utf8');
    const calculatedSignature = hmac.digest('base64');
    
    return calculatedSignature === signature;
  }
}
