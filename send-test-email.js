require('dotenv').config();

// Import the services directly
const { CustomerNotificationService } = require('./dist/services/CustomerNotificationService');
const { SupplierNotificationService } = require('./dist/services/SupplierNotificationService');

class DirectEmailTester {
  constructor() {
    this.customerService = new CustomerNotificationService();
    this.supplierService = new SupplierNotificationService();
    this.testEmail = '<EMAIL>';
  }

  /**
   * Send beautiful customer email test
   */
  async sendCustomerEmailTest() {
    console.log('📧 Sending beautiful customer email test...\n');

    const customerData = {
      returnId: 'TEST-CUSTOMER-' + Date.now(),
      orderId: 'ORDER-12345',
      orderNumber: '#BEAUTIFUL-TEST-001',
      customerEmail: this.testEmail,
      customerName: 'Feranmi Olawore',
      items: [
        {
          name: 'Vessel - Air [Emerald] Premium Vaporizer',
          sku: 'vessel-air-emerald-001',
          qty: 2,
          reason: 'Product arrived damaged - packaging was torn',
          product_id: '9698464760088',
          variant_id: '49924238278936'
        },
        {
          name: 'GRAV® Helix™ Chillum - Clear Glass',
          sku: 'grav-helix-chillum-clear',
          qty: 1,
          reason: 'Wrong size ordered - customer wanted larger version',
          product_id: '9698464760089',
          variant_id: '49924238278937'
        }
      ],
      requestType: 'return',
      submittedDate: new Date().toISOString(),
      estimatedProcessingTime: '3-5 business days',
      trackingInfo: {
        returnLabel: 'Available in customer portal',
        trackingNumber: 'TEST123456789',
        carrierName: 'UPS'
      },
      notes: 'Customer is a VIP member - please prioritize this return request'
    };

    try {
      const result = await this.customerService.notifyCustomer(customerData);
      
      if (result.success) {
        console.log('✅ Customer email sent successfully!');
        console.log(`📧 Sent to: ${this.testEmail}`);
        console.log(`📨 Message ID: ${result.messageId}`);
        console.log('\n🎨 Email Features:');
        console.log('   • Beautiful gradient header with animations');
        console.log('   • 3D-style status badges and cards');
        console.log('   • Progress timeline with visual indicators');
        console.log('   • Professional Inter font typography');
        console.log('   • Mobile-responsive design');
        console.log('   • Interactive buttons and CTAs');
        return true;
      } else {
        console.log('❌ Failed to send customer email:', result.error);
        return false;
      }
    } catch (error) {
      console.log('❌ Error sending customer email:', error.message);
      return false;
    }
  }

  /**
   * Send beautiful supplier email test
   */
  async sendSupplierEmailTest() {
    console.log('📧 Sending beautiful supplier email test...\n');

    // Mock vendor info with high priority
    const vendorInfo = {
      name: 'Vessel',
      priority: 'HIGH',
      verified: true,
      contact: {
        email: this.testEmail, // Send to test email
        phone: '******-VESSEL',
        website: 'https://vessel.com',
        address: {
          street: '123 Vessel Street',
          city: 'San Francisco',
          state: 'CA',
          zip: '94102',
          country: 'USA'
        },
        businessHours: 'Mon-Fri 9AM-6PM PST',
        timezone: 'PST'
      },
      returnPolicy: {
        url: 'https://vessel.com/returns',
        timeLimit: '30 days',
        requirements: 'Original packaging required, unused condition',
        restockingFee: '15% for opened items',
        returnAddress: '123 Vessel Street, San Francisco, CA 94102'
      },
      products: {
        count: 25,
        categories: ['Vaporizers', 'Accessories'],
        tags: ['premium', 'portable', 'battery']
      },
      automation: {
        emailTemplate: 'vessel-return',
        requiresApproval: true,
        autoRefund: false,
        processingTime: '2-3 business days'
      },
      notes: 'Premium supplier - high priority processing required'
    };

    const supplierData = {
      returnId: 'TEST-SUPPLIER-' + Date.now(),
      orderId: 'ORDER-12345',
      orderNumber: '#BEAUTIFUL-TEST-001',
      customerEmail: '<EMAIL>',
      customerName: 'Feranmi Olawore',
      customerAddress: {
        street: '456 Customer Lane',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'USA'
      },
      items: [
        {
          name: 'Vessel - Air [Emerald] Premium Vaporizer',
          sku: 'vessel-air-emerald-001',
          qty: 2,
          reason: 'Product arrived damaged - packaging was torn',
          product_id: '9698464760088',
          variant_id: '49924238278936'
        },
        {
          name: 'Vessel - Compass [Black] Portable Vaporizer',
          sku: 'vessel-compass-black-001',
          qty: 1,
          reason: 'Customer changed mind - wants different color',
          product_id: '9698464760090',
          variant_id: '49924238278938'
        }
      ],
      totalRefundAmount: 269.97,
      refundMethod: 'Original payment method',
      returnReason: 'Damaged product and color preference',
      returnDate: new Date().toISOString(),
      notes: 'VIP customer - please handle with priority. Customer mentioned packaging was damaged during shipping.'
    };

    try {
      const result = await this.supplierService.notifySupplier(vendorInfo, supplierData);
      
      if (result.success) {
        console.log('✅ Supplier email sent successfully!');
        console.log(`📧 Sent to: ${this.testEmail}`);
        console.log(`📨 Message ID: ${result.messageId}`);
        console.log('\n🎨 Email Features:');
        console.log('   • Urgent red gradient header with alerts');
        console.log('   • HIGH priority color coding');
        console.log('   • Professional business layout');
        console.log('   • Step-by-step action guides');
        console.log('   • Interactive contact buttons');
        console.log('   • Return policy integration');
        return true;
      } else {
        console.log('❌ Failed to send supplier email:', result.error);
        return false;
      }
    } catch (error) {
      console.log('❌ Error sending supplier email:', error.message);
      return false;
    }
  }

  /**
   * Test email connection
   */
  async testEmailConnection() {
    console.log('🔗 Testing email connection...\n');

    try {
      const customerTest = await this.customerService.testEmailConnection();
      const supplierTest = await this.supplierService.testEmailConnection();

      console.log(`Customer Email Service: ${customerTest ? '✅ Connected' : '❌ Failed'}`);
      console.log(`Supplier Email Service: ${supplierTest ? '✅ Connected' : '❌ Failed'}`);

      return customerTest && supplierTest;
    } catch (error) {
      console.log('❌ Email connection test failed:', error.message);
      return false;
    }
  }

  /**
   * Run all email tests
   */
  async runAllTests() {
    console.log('🚀 Starting Beautiful Email Tests...\n');
    console.log(`📧 Test Email Address: ${this.testEmail}\n`);
    console.log('='.repeat(60) + '\n');

    // Test connection first
    console.log('TEST 1: Email Connection');
    console.log('-'.repeat(30));
    const connectionOk = await this.testEmailConnection();
    
    if (!connectionOk) {
      console.log('\n❌ Email connection failed. Please check your email configuration.');
      return false;
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test customer email
    console.log('TEST 2: Customer Email (Beautiful Confirmation)');
    console.log('-'.repeat(30));
    const customerSuccess = await this.sendCustomerEmailTest();

    console.log('\n' + '='.repeat(60) + '\n');

    // Test supplier email
    console.log('TEST 3: Supplier Email (Professional Alert)');
    console.log('-'.repeat(30));
    const supplierSuccess = await this.sendSupplierEmailTest();

    console.log('\n' + '='.repeat(60) + '\n');

    // Summary
    console.log('📊 EMAIL TEST SUMMARY');
    console.log('-'.repeat(30));
    
    const allSuccess = customerSuccess && supplierSuccess;
    
    console.log(`Overall Result: ${allSuccess ? '✅ ALL EMAILS SENT' : '⚠️ SOME EMAILS FAILED'}`);
    console.log(`Customer Email: ${customerSuccess ? '✅ SENT' : '❌ FAILED'}`);
    console.log(`Supplier Email: ${supplierSuccess ? '✅ SENT' : '❌ FAILED'}`);
    
    if (allSuccess) {
      console.log('\n🎉 SUCCESS! Check your <NAME_EMAIL>');
      console.log('\n📧 You should receive:');
      console.log('   1. 🎨 Beautiful Customer Confirmation Email');
      console.log('      • Gradient headers with animations');
      console.log('      • 3D status badges and timeline');
      console.log('      • Professional typography and layout');
      console.log('      • Mobile-responsive design');
      
      console.log('\n   2. 🚨 Professional Supplier Alert Email');
      console.log('      • Urgent red gradient header');
      console.log('      • HIGH priority color coding');
      console.log('      • Step-by-step action guides');
      console.log('      • Interactive contact buttons');
      
      console.log('\n💡 Email Features:');
      console.log('   • CSS3 gradients and animations');
      console.log('   • Box shadows for 3D depth');
      console.log('   • Professional Inter font family');
      console.log('   • Priority-based color schemes');
      console.log('   • Mobile-responsive layouts');
      console.log('   • Interactive hover effects');
    } else {
      console.log('\n❌ Some emails failed to send');
      console.log('🔧 Please check:');
      console.log('   • Email configuration in .env file');
      console.log('   • SMTP server settings');
      console.log('   • Network connectivity');
      console.log('   • Email service authentication');
    }

    console.log('\n🏁 Email test completed!');
    return allSuccess;
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new DirectEmailTester();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('💥 Email test runner failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  });
}

module.exports = DirectEmailTester;
