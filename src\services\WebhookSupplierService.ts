import { MongoSupplierService } from './MongoSupplierService';
import { VendorDatabaseService, VendorInfo } from './VendorDatabaseService';
import { ISupplier } from '../models/Supplier';
import logger from '../utils/logger';

/**
 * Service to handle supplier lookup for webhook processing
 * Integrates MongoDB suppliers with existing vendor database fallback
 */
export class WebhookSupplierService {
  private mongoSupplierService: MongoSupplierService;
  private vendorDatabaseService: VendorDatabaseService;

  constructor() {
    this.mongoSupplierService = new MongoSupplierService();
    this.vendorDatabaseService = new VendorDatabaseService();
  }

  /**
   * Get supplier information by vendor name from webhook
   * First tries MongoDB, then falls back to JSON vendor database
   */
  async getSupplierByVendorName(vendorName: string): Promise<VendorInfo | null> {
    try {
      // First try MongoDB
      const mongoSupplier = await this.mongoSupplierService.getSupplierByVendorName(vendorName);
      
      if (mongoSupplier) {
        logger.info('Supplier found in MongoDB', {
          vendorName,
          supplierName: mongoSupplier.name,
          supplierId: mongoSupplier._id
        });
        
        return this.convertMongoSupplierToVendorInfo(mongoSupplier);
      }

      // Fallback to JSON vendor database
      logger.debug('Supplier not found in MongoDB, checking JSON database', { vendorName });
      const jsonVendor = await this.vendorDatabaseService.getVendor(vendorName);
      
      if (jsonVendor) {
        logger.info('Supplier found in JSON database', {
          vendorName,
          supplierName: jsonVendor.name
        });
        return jsonVendor;
      }

      logger.warn('Supplier not found in any database', { vendorName });
      return null;
    } catch (error: any) {
      logger.error('Failed to get supplier by vendor name', {
        error: error.message,
        vendorName
      });
      return null;
    }
  }

  /**
   * Get suppliers by product tags
   * Useful for webhook processing when vendor name is not clear
   */
  async getSuppliersByTags(tags: string[]): Promise<VendorInfo[]> {
    try {
      const mongoSuppliers = await this.mongoSupplierService.getSuppliersByTags(tags);
      
      const vendorInfos = mongoSuppliers.map(supplier => 
        this.convertMongoSupplierToVendorInfo(supplier)
      );

      logger.info('Suppliers found by tags', {
        tags,
        count: vendorInfos.length,
        suppliers: vendorInfos.map(v => v.name)
      });

      return vendorInfos;
    } catch (error: any) {
      logger.error('Failed to get suppliers by tags', {
        error: error.message,
        tags
      });
      return [];
    }
  }

  /**
   * Smart supplier lookup that tries multiple strategies
   * 1. Direct vendor name match
   * 2. Partial vendor name match
   * 3. Tag-based matching
   */
  async smartSupplierLookup(
    vendorName?: string,
    productTags?: string[],
    productTitle?: string
  ): Promise<VendorInfo | null> {
    try {
      // Strategy 1: Direct vendor name match
      if (vendorName) {
        const directMatch = await this.getSupplierByVendorName(vendorName);
        if (directMatch) {
          logger.info('Supplier found via direct vendor name match', {
            vendorName,
            supplierName: directMatch.name
          });
          return directMatch;
        }

        // Strategy 2: Partial vendor name match
        const partialMatch = await this.findSupplierByPartialName(vendorName);
        if (partialMatch) {
          logger.info('Supplier found via partial vendor name match', {
            vendorName,
            supplierName: partialMatch.name
          });
          return partialMatch;
        }
      }

      // Strategy 3: Tag-based matching
      if (productTags && productTags.length > 0) {
        const tagMatches = await this.getSuppliersByTags(productTags);
        if (tagMatches.length > 0) {
          logger.info('Suppliers found via tag matching', {
            productTags,
            count: tagMatches.length,
            selectedSupplier: tagMatches[0].name
          });
          return tagMatches[0]; // Return first match
        }
      }

      // Strategy 4: Product title keyword matching
      if (productTitle) {
        const titleMatch = await this.findSupplierByProductTitle(productTitle);
        if (titleMatch) {
          logger.info('Supplier found via product title matching', {
            productTitle,
            supplierName: titleMatch.name
          });
          return titleMatch;
        }
      }

      logger.warn('No supplier found using any strategy', {
        vendorName,
        productTags,
        productTitle
      });

      return null;
    } catch (error: any) {
      logger.error('Smart supplier lookup failed', {
        error: error.message,
        vendorName,
        productTags,
        productTitle
      });
      return null;
    }
  }

  /**
   * Find supplier by partial name matching
   */
  private async findSupplierByPartialName(vendorName: string): Promise<VendorInfo | null> {
    try {
      const searchResults = await this.mongoSupplierService.searchSuppliers(vendorName);
      
      if (searchResults.length > 0) {
        return this.convertMongoSupplierToVendorInfo(searchResults[0]);
      }

      return null;
    } catch (error: any) {
      logger.error('Failed to find supplier by partial name', {
        error: error.message,
        vendorName
      });
      return null;
    }
  }

  /**
   * Find supplier by product title keywords
   */
  private async findSupplierByProductTitle(productTitle: string): Promise<VendorInfo | null> {
    try {
      // Extract potential brand/vendor keywords from product title
      const keywords = this.extractKeywordsFromTitle(productTitle);
      
      for (const keyword of keywords) {
        const supplier = await this.getSupplierByVendorName(keyword);
        if (supplier) {
          return supplier;
        }
      }

      return null;
    } catch (error: any) {
      logger.error('Failed to find supplier by product title', {
        error: error.message,
        productTitle
      });
      return null;
    }
  }

  /**
   * Extract potential brand/vendor keywords from product title
   */
  private extractKeywordsFromTitle(title: string): string[] {
    // Common brand indicators and patterns
    const brandPatterns = [
      /^([A-Z][a-z]+)/,  // Capitalized word at start
      /\b([A-Z]{2,})\b/g, // All caps words
      /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b/g // Title case phrases
    ];

    const keywords: string[] = [];
    
    for (const pattern of brandPatterns) {
      const matches = title.match(pattern);
      if (matches) {
        keywords.push(...matches);
      }
    }

    // Remove duplicates and common words
    const commonWords = ['the', 'and', 'or', 'with', 'for', 'by', 'from'];
    return [...new Set(keywords)]
      .filter(keyword => !commonWords.includes(keyword.toLowerCase()))
      .slice(0, 5); // Limit to first 5 keywords
  }

  /**
   * Convert MongoDB supplier to VendorInfo format for compatibility
   */
  private convertMongoSupplierToVendorInfo(supplier: ISupplier): VendorInfo {
    return {
      name: supplier.name,
      priority: supplier.priority,
      verified: supplier.verified,
      contact: {
        email: supplier.contact.email,
        phone: supplier.contact.phone,
        website: supplier.contact.website,
        address: supplier.contact.address,
        businessHours: supplier.contact.businessHours,
        timezone: supplier.contact.timezone
      },
      returnPolicy: supplier.returnPolicy,
      products: supplier.products,
      automation: {
        emailTemplate: supplier.automation.emailTemplate,
        requiresApproval: supplier.automation.requiresApproval,
        autoRefund: supplier.automation.autoRefund,
        processingTime: supplier.automation.processingTime
      },
      notes: supplier.notes
    };
  }

  /**
   * Get all active suppliers for admin purposes
   */
  async getAllActiveSuppliers(): Promise<VendorInfo[]> {
    try {
      const mongoSuppliers = await this.mongoSupplierService.getAllActiveSuppliers();
      return mongoSuppliers.map(supplier => this.convertMongoSupplierToVendorInfo(supplier));
    } catch (error: any) {
      logger.error('Failed to get all active suppliers', {
        error: error.message
      });
      return [];
    }
  }

  /**
   * Get supplier statistics
   */
  async getSupplierStats(): Promise<{
    total: number;
    active: number;
    verified: number;
    byPriority: { HIGH: number; MEDIUM: number; LOW: number };
  }> {
    try {
      return await this.mongoSupplierService.getSupplierStats();
    } catch (error: any) {
      logger.error('Failed to get supplier stats', {
        error: error.message
      });
      return {
        total: 0,
        active: 0,
        verified: 0,
        byPriority: { HIGH: 0, MEDIUM: 0, LOW: 0 }
      };
    }
  }
}
