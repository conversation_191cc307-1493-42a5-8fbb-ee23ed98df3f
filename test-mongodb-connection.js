const mongoose = require('mongoose');
require('dotenv').config();

async function testMongoDBConnection() {
  console.log('🧪 Testing MongoDB Connection...\n');

  // Get configuration
  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
  const DB_NAME = process.env.MONGODB_DB_NAME || 'shopify_automation';

  console.log('📋 Configuration:');
  console.log(`   URI: ${MONGODB_URI.replace(/\/\/.*@/, '//***:***@')}`);
  console.log(`   Database: ${DB_NAME}\n`);

  try {
    // Build connection URI
    let mongoUri;
    if (MONGODB_URI.includes('mongodb+srv://') || MONGODB_URI.includes('@')) {
      // Atlas or authenticated URI
      mongoUri = MONGODB_URI.includes('?') 
        ? MONGODB_URI.replace('?', `/${DB_NAME}?`)
        : `${MONGODB_URI}/${DB_NAME}`;
    } else {
      // Local MongoDB
      mongoUri = `${MONGODB_URI}/${DB_NAME}`;
    }

    console.log('🔌 Attempting to connect...');
    
    await mongoose.connect(mongoUri, {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      bufferCommands: false,
      bufferMaxEntries: 0
    });

    console.log('✅ MongoDB connection successful!');
    console.log(`   Ready State: ${mongoose.connection.readyState}`);
    console.log(`   Host: ${mongoose.connection.host}`);
    console.log(`   Database: ${mongoose.connection.name}`);

    // Test basic operations
    console.log('\n🧪 Testing basic operations...');
    
    // Test ping
    await mongoose.connection.db.admin().ping();
    console.log('✅ Ping successful');

    // Test database stats
    const stats = await mongoose.connection.db.stats();
    console.log('✅ Database stats retrieved');
    console.log(`   Collections: ${stats.collections}`);
    console.log(`   Documents: ${stats.objects}`);

    console.log('\n🎉 All tests passed! MongoDB is ready to use.');
    console.log('\n📝 Next steps:');
    console.log('   1. Run: node migrate-vendors-to-mongodb.js');
    console.log('   2. Run: npm run build');
    console.log('   3. Run: npm start');
    console.log('   4. Open: http://localhost:3000/dashboard');

  } catch (error) {
    console.error('❌ MongoDB connection failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.message.includes('Authentication failed') || error.message.includes('bad auth')) {
      console.log('\n💡 Authentication Error Solutions:');
      console.log('   1. Check your username and password in MONGODB_URI');
      console.log('   2. Ensure the user has proper permissions');
      console.log('   3. For Atlas: Check database access settings');
    } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Connection Error Solutions:');
      console.log('   1. Check if MongoDB is running locally');
      console.log('   2. Verify the connection URI is correct');
      console.log('   3. For Atlas: Check network access settings');
    } else if (error.message.includes('timeout')) {
      console.log('\n💡 Timeout Error Solutions:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify firewall settings');
      console.log('   3. For Atlas: Ensure IP is whitelisted');
    }

    console.log('\n🔧 Quick Setup Options:');
    console.log('\n   Option 1: MongoDB Atlas (Cloud)');
    console.log('   - Go to https://www.mongodb.com/atlas');
    console.log('   - Create free cluster');
    console.log('   - Get connection string');
    console.log('   - Update MONGODB_URI in .env');
    
    console.log('\n   Option 2: Local MongoDB');
    console.log('   - Install MongoDB Community Server');
    console.log('   - Start MongoDB service');
    console.log('   - Use: MONGODB_URI=mongodb://localhost:27017');
    
    console.log('\n   Option 3: Docker MongoDB');
    console.log('   - Run: docker run -d --name mongodb -p 27017:27017 mongo:latest');
    console.log('   - Use: MONGODB_URI=mongodb://localhost:27017');

    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n📡 Disconnected from MongoDB');
  }
}

// Run test
testMongoDBConnection();
