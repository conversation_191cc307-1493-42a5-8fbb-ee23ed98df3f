import nodemailer from 'nodemailer';
import { ReturnPrimeWebhookPayload, TransformedReturnPayload, ReturnItem } from '../types';
import { VendorDatabaseService, VendorInfo } from './VendorDatabaseService';
import { SupplierNotificationService, SupplierNotificationData } from './SupplierNotificationService';
import { CustomerNotificationService, CustomerNotificationData } from './CustomerNotificationService';
import { ShopifyService } from './ShopifyService';
import { transformReturnPrimePayload, getEventTypeFromHeaders } from '../utils/transform';
import { config } from '../config';
import logger from '../utils/logger';
import { addLogEntry } from '../routes/logs';

// Import the Shopify Product Fetcher (legacy)
const ShopifyProductFetcher = require('../../shopify-product-fetcher.js');

export interface ProcessedReturnItem {
  item: ReturnItem;
  vendor?: string;
  vendorInfo?: VendorInfo;
  shopifyProduct?: any;
  shopifyVariant?: any;
  notificationSent: boolean;
  error?: string;
}

export interface ProcessedReturn {
  returnId: string;
  orderId: string;
  customerEmail: string;
  customerName?: string;
  items: ProcessedReturnItem[];
  totalItems: number;
  processedItems: number;
  notificationsSent: number;
  customerNotified: boolean;
  errors: string[];
}

export class EnhancedReturnPrimeService {
  private shopifyFetcher: any;
  private shopifyService: ShopifyService;
  private vendorDatabase: VendorDatabaseService;
  private notificationService: SupplierNotificationService;
  private customerNotificationService: CustomerNotificationService;
  private transporter: nodemailer.Transporter;

  constructor() {
    this.shopifyFetcher = new ShopifyProductFetcher();
    this.shopifyService = new ShopifyService();
    this.vendorDatabase = new VendorDatabaseService();
    this.notificationService = new SupplierNotificationService();
    this.customerNotificationService = new CustomerNotificationService();

    // Initialize email transporter
    this.transporter = nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.port === 465, // true for 465, false for other ports
      auth: {
        user: config.email.user,
        pass: config.email.pass
      },
      tls: {
        rejectUnauthorized: false
      }
    });
  }

  /**
   * Process Return Prime webhook for refund requests
   */
  public async processRefundWebhook(
    payload: ReturnPrimeWebhookPayload,
    headers: Record<string, any>
  ): Promise<{
    success: boolean;
    processedReturn?: ProcessedReturn;
    error?: string;
  }> {
    try {
      // Transform payload to internal format
      const transformedPayload = transformReturnPrimePayload(payload);
      const eventType = getEventTypeFromHeaders(headers);

      logger.info('Processing Return Prime refund webhook', {
        returnId: transformedPayload.return_id,
        eventType,
        itemCount: transformedPayload.items.length
      });

      // Add real-time log entry for dashboard
      addLogEntry('info', `Return Prime webhook received for return ${transformedPayload.return_id}`, {
        returnId: transformedPayload.return_id,
        orderId: transformedPayload.order_id,
        eventType,
        itemCount: transformedPayload.items.length,
        customerEmail: transformedPayload.customer_email
      });

      // Process all return events (request.created, refund events, etc.)
      logger.info('✅ UPDATED: Processing ALL return events (no more skipping!)', {
        returnId: transformedPayload.return_id,
        eventType,
        supportedVendors: ['Buddify', 'SmokeDrop'],
        version: 'v2.0-no-skip',
        message: 'Now processing request.created and all other return events'
      });

      // Process the return
      const processedReturn = await this.processReturn(transformedPayload);

      // Add success log entry
      addLogEntry('success', `Return ${transformedPayload.return_id} processed successfully`, {
        returnId: transformedPayload.return_id,
        orderId: transformedPayload.order_id,
        processedItems: processedReturn.processedItems,
        notificationsSent: processedReturn.notificationsSent,
        customerEmail: transformedPayload.customer_email
      });

      return {
        success: true,
        processedReturn
      };
    } catch (error: any) {
      logger.error('Failed to process Return Prime refund webhook', {
        error: error.message,
        payload: JSON.stringify(payload, null, 2)
      });

      // Add error log entry for dashboard
      addLogEntry('error', `Failed to process Return Prime webhook: ${error.message}`, {
        error: error.message,
        payload: payload
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process a return request and notify suppliers
   */
  private async processReturn(payload: TransformedReturnPayload): Promise<ProcessedReturn> {
    const processedReturn: ProcessedReturn = {
      returnId: payload.return_id,
      orderId: payload.order_id,
      customerEmail: payload.customer_email,
      customerName: payload.customer_name,
      items: [],
      totalItems: payload.items.length,
      processedItems: 0,
      notificationsSent: 0,
      customerNotified: false,
      errors: []
    };

    try {
      // Process each item using webhook data first, then Shopify if needed
      for (const item of payload.items) {
        const processedItem: ProcessedReturnItem = {
          item,
          vendor: undefined,
          vendorInfo: undefined,
          shopifyProduct: undefined,
          shopifyVariant: undefined,
          notificationSent: false,
          error: undefined
        };

        try {
          // Step 1: Try to extract vendor from webhook data first (only Buddify and SmokeDrop)
          let vendorName: string | undefined;

          // Try to get vendor from item data if available in webhook
          vendorName = this.extractVendorFromSKU(item.sku);

          if (!vendorName) {
            vendorName = this.extractVendorFromProductTitle(item.name);
          }

          // Step 2: Always check Shopify product tags for vendor identification
          logger.info('Checking Shopify product tags for vendor identification', {
            currentVendor: vendorName,
            productId: item.product_id,
            variantId: item.variant_id,
            sku: item.sku
          });

          // Call Shopify API to get product details and tags
          const shopifyResult = await this.getShopifyProductWithTags(item);

          if (shopifyResult.success && shopifyResult.tags) {
            const tagBasedVendor = this.extractVendorFromTags(shopifyResult.tags);
            if (tagBasedVendor && this.isSupportedVendor(tagBasedVendor)) {
              vendorName = tagBasedVendor;
              logger.info('Vendor identified from Shopify tags', {
                vendor: vendorName,
                tags: shopifyResult.tags,
                productTitle: shopifyResult.product?.title
              });
            } else if (!vendorName || !this.isSupportedVendor(vendorName)) {
              // If no supported vendor found in tags either, log and skip
              logger.info('No supported vendor found in Shopify tags', {
                tags: shopifyResult.tags,
                productTitle: shopifyResult.product?.title,
                supportedVendors: ['Buddify', 'SmokeDrop']
              });
            }
          }

          // Step 2: If no vendor found, call Shopify to get vendor info
          if (!vendorName) {
            logger.info('🔍 No vendor found in webhook data, fetching from Shopify', {
              sku: item.sku,
              productId: item.product_id,
              variantId: item.variant_id
            });

            let shopifyResult;

            // Try SKU first, then variant_id
            if (item.sku) {
              shopifyResult = await this.shopifyFetcher.fetchBySKU(item.sku);
            }

            if (!shopifyResult?.success && item.variant_id) {
              shopifyResult = await this.shopifyFetcher.fetchByVariantId(item.variant_id);
            }

            if (shopifyResult?.success) {
              processedItem.shopifyProduct = shopifyResult.product;
              processedItem.shopifyVariant = shopifyResult.variant;
              vendorName = shopifyResult.vendor;

              logger.info('✅ Found vendor from Shopify', {
                sku: item.sku,
                vendor: vendorName,
                productTitle: shopifyResult.product.title,
                source: shopifyResult.source
              });
            } else {
              logger.warn('❌ Could not find product in Shopify', {
                sku: item.sku,
                variantId: item.variant_id,
                error: shopifyResult?.error
              });
            }
          }

          // Step 3: Validate vendor is supported (only Buddify and SmokeDrop)
          if (vendorName && !this.isSupportedVendor(vendorName)) {
            logger.warn('Vendor not supported, skipping item', {
              sku: item.sku,
              vendor: vendorName,
              supportedVendors: ['Buddify', 'SmokeDrop'],
              returnId: payload.return_id
            });
            processedItem.error = `Vendor '${vendorName}' not supported (only Buddify and SmokeDrop are supported)`;
            processedReturn.items.push(processedItem);
            continue;
          }

          if (vendorName) {
            // Get vendor info from our database
            const vendorInfo = await this.vendorDatabase.getVendor(vendorName);

            if (vendorInfo) {
              processedItem.vendor = vendorName;
              processedItem.vendorInfo = vendorInfo;

              logger.debug('Found vendor for item from webhook data', {
                sku: item.sku,
                vendor: vendorName,
                hasVendorInfo: !!vendorInfo
              });
            } else {
              processedItem.error = `Vendor '${vendorName}' not found in database`;
              logger.warn('Vendor not found in database', {
                sku: item.sku,
                vendor: vendorName
              });
            }
          } else {
            processedItem.error = 'Could not determine vendor from webhook data';
            logger.warn('No vendor found for item', {
              sku: item.sku,
              productName: item.name
            });
          }

          processedReturn.processedItems++;
        } catch (error: any) {
          processedItem.error = `Failed to process vendor: ${error.message}`;
          logger.error('Error processing item vendor', {
            sku: item.sku,
            error: error.message
          });
        }

        processedReturn.items.push(processedItem);
      }

      // Group items by vendor for notification
      const vendorGroups = this.groupItemsByVendor(processedReturn.items);

      // Process notifications for each vendor
      for (const [vendorName, vendorData] of Object.entries(vendorGroups)) {
        try {
          if (!vendorData.vendorInfo) {
            logger.warn('No vendor info available for notification', {
              vendor: vendorName,
              itemCount: vendorData.items.length
            });

            // Mark items as failed
            vendorData.items.forEach(processedItem => {
              processedItem.error = 'No vendor contact information available';
            });

            processedReturn.errors.push(`No contact info for vendor: ${vendorName}`);
            continue;
          }

          // Create notification data with all webhook data
          const notificationData: SupplierNotificationData = {
            supplier: vendorName,
            email: vendorData.vendorInfo.contact.email,
            returnId: payload.return_id,
            orderId: payload.order_id,
            orderNumber: payload.order_number,
            customerEmail: payload.customer_email,
            customerName: payload.customer_name,
            customerAddress: payload.customer_address,
            items: vendorData.items.map(pi => pi.item),
            createdAt: payload.created_at,
            requiresManualReview: vendorData.vendorInfo.automation?.requiresApproval || false,
            vendorInfo: vendorData.vendorInfo,
            shopifyProducts: vendorData.items.map(pi => ({
              product: pi.shopifyProduct,
              variant: pi.shopifyVariant
            })).filter(sp => sp.product), // Only include items with Shopify data
            totalRefundAmount: payload.refund_amount,
            refundMethod: payload.refund_method || 'Original payment method', // Dynamic from webhook
            returnDate: payload.created_at,
            notes: payload.notes
          };

          // 1. Send supplier notification (environment-based routing)
          logger.info('Sending supplier notification with environment-based routing', {
            vendor: vendorName,
            email: vendorData.vendorInfo.contact.email,
            itemCount: vendorData.items.length,
            returnId: payload.return_id,
            environment: config.nodeEnv
          });

          // Send supplier notification using environment-based routing
          const supplierResult = await this.notificationService.notifySupplier(
            vendorData.vendorInfo,
            notificationData
          );

          if (!supplierResult.success) {
            logger.error('Failed to send supplier notification', {
              vendor: vendorName,
              error: supplierResult.error
            });

            // Add error log entry for dashboard
            addLogEntry('error', `Failed to notify supplier ${vendorName}: ${supplierResult.error}`, {
              vendor: vendorName,
              returnId: payload.return_id,
              error: supplierResult.error
            });
          } else {
            // Add success log entry for dashboard
            addLogEntry('success', `Supplier ${vendorName} notified successfully`, {
              vendor: vendorName,
              returnId: payload.return_id,
              email: vendorData.vendorInfo.contact.email,
              itemCount: vendorData.items.length
            });
          }

          // Mark items as notified
          vendorData.items.forEach(processedItem => {
            processedItem.notificationSent = supplierResult.success;
          });

          if (supplierResult.success) {
            processedReturn.notificationsSent++;

            logger.info('Supplier notification sent successfully', {
              vendor: vendorName,
              email: vendorData.vendorInfo.contact.email,
              itemCount: vendorData.items.length,
              environment: config.nodeEnv,
              messageId: supplierResult.messageId
            });
          }

        } catch (error: any) {
          vendorData.items.forEach(processedItem => {
            processedItem.error = `Notification error: ${error.message}`;
          });

          processedReturn.errors.push(`Error processing ${vendorName}: ${error.message}`);

          logger.error('Error processing vendor notification', {
            vendor: vendorName,
            error: error.message
          });
        }
      }

      // Send customer notification after processing suppliers
      try {
        const customerNotificationData: CustomerNotificationData = {
          returnId: payload.return_id,
          orderId: payload.order_id,
          orderNumber: payload.order_number,
          customerEmail: payload.customer_email,
          customerName: payload.customer_name,
          items: payload.items,
          requestType: payload.exchange ? 'exchange' : 'return', // Dynamic based on webhook
          submittedDate: payload.created_at,
          estimatedProcessingTime: undefined, // Let template use default or dynamic value
          notes: payload.notes
        };

        // 3. ENABLED: Send customer notification to actual customer email from webhook
        logger.info('Sending customer notification to actual customer email', {
          customerEmail: payload.customer_email,
          returnId: payload.return_id
        });

        const customerResult = await this.customerNotificationService.notifyCustomer(customerNotificationData);
        processedReturn.customerNotified = customerResult.success;

        if (customerResult.success) {
          logger.info('Customer notification sent successfully', {
            customerEmail: payload.customer_email,
            returnId: payload.return_id,
            messageId: customerResult.messageId
          });
        } else {
          logger.error('Failed to send customer notification', {
            customerEmail: payload.customer_email,
            returnId: payload.return_id,
            error: customerResult.error
          });
        }

      } catch (error: any) {
        processedReturn.errors.push(`Customer notification error: ${error.message}`);
        logger.error('Error sending customer notification', {
          customerEmail: payload.customer_email,
          returnId: payload.return_id,
          error: error.message
        });
      }

      logger.info('Return processing completed', {
        returnId: payload.return_id,
        totalItems: processedReturn.totalItems,
        processedItems: processedReturn.processedItems,
        notificationsSent: processedReturn.notificationsSent,
        customerNotified: processedReturn.customerNotified,
        errors: processedReturn.errors.length
      });

      return processedReturn;
    } catch (error: any) {
      logger.error('Error processing return', {
        returnId: payload.return_id,
        error: error.message
      });

      processedReturn.errors.push(`Processing error: ${error.message}`);
      return processedReturn;
    }
  }

  /**
   * Group processed items by vendor
   */
  private groupItemsByVendor(
    processedItems: ProcessedReturn['items']
  ): Record<string, {
    vendorInfo?: VendorInfo;
    items: ProcessedReturn['items'];
  }> {
    const groups: Record<string, {
      vendorInfo?: VendorInfo;
      items: ProcessedReturn['items'];
    }> = {};

    for (const processedItem of processedItems) {
      if (processedItem.vendor) {
        if (!groups[processedItem.vendor]) {
          groups[processedItem.vendor] = {
            vendorInfo: processedItem.vendorInfo,
            items: []
          };
        }
        groups[processedItem.vendor].items.push(processedItem);
      }
    }

    return groups;
  }

  /**
   * Get return processing statistics
   */
  public async getProcessingStats(dateFrom?: string, dateTo?: string): Promise<{
    totalReturns: number;
    processedReturns: number;
    notificationsSent: number;
    topVendors: Array<{ vendor: string; returnCount: number }>;
  }> {
    // This would typically query a database of processed returns
    // For now, return basic stats from vendor database
    try {
      const stats = await this.vendorDatabase.getDatabaseStats();
      
      return {
        totalReturns: 0, // Would come from return processing logs
        processedReturns: 0, // Would come from return processing logs
        notificationsSent: 0, // Would come from notification logs
        topVendors: [] // Would come from return processing logs
      };
    } catch (error: any) {
      logger.error('Failed to get processing stats', { error: error.message });
      return {
        totalReturns: 0,
        processedReturns: 0,
        notificationsSent: 0,
        topVendors: []
      };
    }
  }

  /**
   * Test the complete workflow
   */
  public async testWorkflow(): Promise<{
    success: boolean;
    tests: Record<string, boolean>;
    errors: string[];
  }> {
    const tests: Record<string, boolean> = {};
    const errors: string[] = [];

    try {
      // Test Shopify connection using our fetcher
      const testResult = await this.shopifyFetcher.fetchBySKU('test-sku-123');
      tests.shopifyConnection = testResult !== null; // Connection works if we get any response
      if (!tests.shopifyConnection) {
        errors.push('Shopify API connection failed');
      }

      // Test vendor database
      try {
        const stats = await this.vendorDatabase.getDatabaseStats();
        tests.vendorDatabase = stats.totalVendors > 0;
        if (!tests.vendorDatabase) {
          errors.push('Vendor database is empty');
        }
      } catch (error: any) {
        tests.vendorDatabase = false;
        errors.push(`Vendor database error: ${error.message}`);
      }

      // Test supplier email service
      tests.supplierEmailService = await this.notificationService.testEmailConnection();
      if (!tests.supplierEmailService) {
        errors.push('Supplier email service connection failed');
      }

      // Test customer email service
      tests.customerEmailService = await this.customerNotificationService.testEmailConnection();
      if (!tests.customerEmailService) {
        errors.push('Customer email service connection failed');
      }

      const allTestsPassed = Object.values(tests).every(test => test);

      logger.info('Workflow test completed', {
        success: allTestsPassed,
        tests,
        errorCount: errors.length
      });

      return {
        success: allTestsPassed,
        tests,
        errors
      };
    } catch (error: any) {
      logger.error('Workflow test failed', { error: error.message });
      return {
        success: false,
        tests,
        errors: [error.message]
      };
    }
  }

  /**
   * Send supplier notification copy to support emails
   */
  private async sendSupplierCopyToSupport(
    vendorInfo: VendorInfo,
    notificationData: SupplierNotificationData,
    supplierTemplate: { subject: string; html: string; text: string }
  ): Promise<void> {
    const supportEmails = ['<EMAIL>'];

    for (const supportEmail of supportEmails) {
      try {
        const mailOptions = {
          from: config.email.from,
          to: supportEmail,
          subject: `[SUPPLIER COPY] ${supplierTemplate.subject}`,
          html: `
            <div style="background-color: #f0f8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #0066cc; margin: 0;">📋 SUPPLIER NOTIFICATION COPY</h3>
              <p style="margin: 10px 0 0 0; color: #666;">
                This is a copy of the notification that would be sent to: <strong>${vendorInfo.contact.email}</strong>
                <br><strong>Note:</strong> Actual supplier notification was SIMULATED (not sent to real company)
              </p>
            </div>
            ${supplierTemplate.html}
          `,
          text: `
[SUPPLIER COPY] - This is a copy of the notification that would be sent to: ${vendorInfo.contact.email}
Note: Actual supplier notification was SIMULATED (not sent to real company)

${supplierTemplate.text}
          `,
          replyTo: config.email.from
        };

        await this.transporter.sendMail(mailOptions);

        logger.info('Supplier copy sent to support', {
          supportEmail,
          vendor: vendorInfo.name,
          returnId: notificationData.returnId
        });
      } catch (error: any) {
        logger.error('Failed to send supplier copy to support', {
          supportEmail,
          vendor: vendorInfo.name,
          returnId: notificationData.returnId,
          error: error.message
        });
      }
    }
  }

  /**
   * Send customer notification copy to support emails
   */
  private async sendCustomerCopyToSupport(
    customerNotificationData: CustomerNotificationData
  ): Promise<void> {
    const supportEmails = ['<EMAIL>'];

    // Generate customer email template
    const customerTemplate = this.customerNotificationService.generateCustomerEmailTemplate(
      customerNotificationData
    );

    for (const supportEmail of supportEmails) {
      try {
        const mailOptions = {
          from: config.email.from,
          to: supportEmail,
          subject: `[CUSTOMER COPY] ${customerTemplate.subject}`,
          html: `
            <div style="background-color: #f0fff0; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #009900; margin: 0;">👤 CUSTOMER NOTIFICATION COPY</h3>
              <p style="margin: 10px 0 0 0; color: #666;">
                This is a copy of the notification sent to customer: <strong>${customerNotificationData.customerEmail}</strong>
              </p>
            </div>
            ${customerTemplate.html}
          `,
          text: `
[CUSTOMER COPY] - This is a copy of the notification sent to customer: ${customerNotificationData.customerEmail}

${customerTemplate.text}
          `,
          replyTo: config.email.from
        };

        await this.transporter.sendMail(mailOptions);

        logger.info('Customer copy sent to support', {
          supportEmail,
          customerEmail: customerNotificationData.customerEmail,
          returnId: customerNotificationData.returnId
        });
      } catch (error: any) {
        logger.error('Failed to send customer copy to support', {
          supportEmail,
          customerEmail: customerNotificationData.customerEmail,
          returnId: customerNotificationData.returnId,
          error: error.message
        });
      }
    }
  }

  /**
   * Extract vendor name from SKU using common patterns
   */
  private extractVendorFromSKU(sku: string): string | undefined {
    // SKU patterns for supported vendors only (Buddify and SmokeDrop)
    const skuPatterns: Record<string, string> = {
      'SD_': 'Smoke Drop',
      'SMOKE_': 'Smoke Drop',
      'SMOKEDROP_': 'Smoke Drop',
      'BUDDIFY_': 'Buddify',
      'BUD_': 'Buddify'
    };

    const skuUpper = sku.toUpperCase();

    for (const [prefix, vendor] of Object.entries(skuPatterns)) {
      if (skuUpper.startsWith(prefix) || skuUpper.includes(prefix.replace('_', ''))) {
        return vendor;
      }
    }

    return undefined;
  }

  /**
   * Extract vendor name from product title using common patterns
   */
  private extractVendorFromProductTitle(productName: string): string | undefined {
    // Title patterns for supported vendors only (Buddify and SmokeDrop)
    const titlePatterns: Record<string, string> = {
      'smoke drop': 'Smoke Drop',
      'smokedrop': 'Smoke Drop',
      'buddify': 'Buddify'
    };

    const lowerTitle = productName.toLowerCase();

    for (const [pattern, vendor] of Object.entries(titlePatterns)) {
      if (lowerTitle.includes(pattern)) {
        return vendor;
      }
    }

    return undefined;
  }

  /**
   * Check if vendor is supported (only Buddify and SmokeDrop)
   */
  private isSupportedVendor(vendorName: string): boolean {
    const supportedVendors = ['Buddify', 'SmokeDrop', 'Smoke Drop'];
    return supportedVendors.includes(vendorName);
  }

  /**
   * Extract vendor from Shopify product tags
   */
  private extractVendorFromTags(tags: string[]): string | undefined {
    const tagLower = tags.map(tag => tag.toLowerCase());

    // Check for vendor names in tags
    if (tagLower.includes('buddify')) return 'Buddify';
    if (tagLower.includes('smokedrop') || tagLower.includes('smoke drop')) return 'Smoke Drop'; // Use database name

    return undefined;
  }

  /**
   * Get Shopify product details with tags
   */
  private async getShopifyProductWithTags(item: any): Promise<{
    success: boolean;
    product?: any;
    tags?: string[];
    vendor?: string;
    error?: string;
  }> {
    try {
      let result;

      // Try to get product details using product_id first
      if (item.product_id) {
        result = await this.shopifyService.getProductDetails(item.product_id.toString());
        if (result.success) {
          return {
            success: true,
            product: result.product,
            tags: result.tags,
            vendor: result.vendor
          };
        }
      }

      // Fallback to SKU or variant_id
      if (item.sku) {
        result = await this.shopifyService.fetchProductBySKU(item.sku);
      } else if (item.variant_id) {
        result = await this.shopifyService.fetchProductByVariantId(Number(item.variant_id));
      }

      if (result && result.success) {
        return {
          success: true,
          product: result.product,
          tags: result.product?.tags || [],
          vendor: result.vendor
        };
      }

      return {
        success: false,
        error: 'Product not found in Shopify'
      };

    } catch (error: any) {
      logger.error('Error fetching Shopify product with tags', {
        error: error.message,
        item: {
          sku: item.sku,
          product_id: item.product_id,
          variant_id: item.variant_id
        }
      });

      return {
        success: false,
        error: error.message
      };
    }
  }
}
