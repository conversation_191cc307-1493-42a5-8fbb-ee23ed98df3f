import Joi from 'joi';
import { ReturnPrimeWebhookPayload } from '../types';

// Flexible Return Prime webhook payload validation schema
// This accepts any structure and validates only what's essential
export const webhookPayloadSchema = Joi.object({
  request: Joi.object({
    id: Joi.string().required(),
    status: Joi.string().optional(), // Accept any status
    created_at: Joi.string().optional(),
    request_number: Joi.string().optional(),
    request_type: Joi.string().optional(),
    customer: Joi.object({
      id: Joi.alternatives().try(Joi.number(), Joi.string()).optional(),
      name: Joi.string().optional(),
      email: Joi.string().email().optional(),
      phone: Joi.string().allow('', null).optional(),
      address: Joi.object().unknown(true).optional() // Accept any address structure
    }).optional(),
    order: Joi.object({
      id: Joi.alternatives().try(Joi.number(), Joi.string()).optional(),
      name: Joi.string().optional(),
      created_at: Joi.string().optional(),
      fulfillments: Joi.array().items(Joi.object().unknown(true)).optional()
    }).optional(),
    line_items: Joi.array().items(
      Joi.object({
        id: Joi.alternatives().try(Joi.number(), Joi.string()).optional(),
        quantity: Joi.number().optional(),
        reason: Joi.string().optional(),
        notes: Joi.string().allow(null, '').optional(),
        original_product: Joi.object({
          product_id: Joi.alternatives().try(Joi.number(), Joi.string()).optional(),
          variant_id: Joi.alternatives().try(Joi.number(), Joi.string()).optional(),
          sku: Joi.string().optional(),
          title: Joi.string().optional(),
          variant_title: Joi.string().allow(null, '').optional(),
          price: Joi.number().optional(),
          image: Joi.object().unknown(true).optional(),
          product_deleted: Joi.boolean().optional(),
          variant_deleted: Joi.boolean().optional()
        }).unknown(true).optional(), // Allow additional fields
        refund: Joi.object({
          status: Joi.string().optional(), // Accept any refund status
          requested_mode: Joi.string().optional(),
          actual_mode: Joi.string().allow(null, '').optional(),
          refunded_at: Joi.string().allow(null, '').optional(),
          comment: Joi.string().allow(null, '').optional()
        }).unknown(true).optional() // Allow additional refund fields
      }).unknown(true) // Allow additional line item fields
    ).optional(),
    // Make all status objects optional and flexible
    approved: Joi.object().unknown(true).optional(),
    rejected: Joi.object().unknown(true).optional(),
    received: Joi.object().unknown(true).optional(),
    inspected: Joi.object().unknown(true).optional(),
    archived: Joi.object().unknown(true).optional(),
    unarchived: Joi.object().unknown(true).optional(),
    manual_request: Joi.boolean().optional(),
    smart_exchange: Joi.boolean().optional()
  }).unknown(true).required() // Allow any additional fields in request
}).unknown(true); // Allow any additional top-level fields

export function validateWebhookPayload(payload: any): { error?: string; value?: ReturnPrimeWebhookPayload } {
  const { error, value } = webhookPayloadSchema.validate(payload, { 
    abortEarly: false,
    stripUnknown: true 
  });
  
  if (error) {
    return { error: error.details.map(d => d.message).join(', ') };
  }
  
  return { value };
}
