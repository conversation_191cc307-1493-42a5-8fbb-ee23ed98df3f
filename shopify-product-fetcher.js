const axios = require('axios');

// Load environment variables
require('dotenv').config();

// Shopify configuration
const SHOPIFY_CONFIG = {
  shop: process.env.SHOPIFY_STORE_URL || 'tgm1vh-mn.myshopify.com',
  accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
  apiVersion: process.env.SHOPIFY_API_VERSION || '2025-07'
};

/**
 * Fetch product data from Shopify using either SKU or variant_id
 */
class ShopifyProductFetcher {
  constructor(config = SHOPIFY_CONFIG) {
    this.config = config;
    this.baseUrl = `https://${config.shop}/admin/api/${config.apiVersion}`;
    this.headers = {
      'X-Shopify-Access-Token': config.accessToken,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Fetch product data by SKU
   * @param {string} sku - Product SKU
   * @returns {Promise<Object>} Product data
   */
  async fetchBySKU(sku) {
    try {
      console.log(`🔍 Searching for product with SKU: ${sku}`);
      
      // Search for products by SKU using the products endpoint
      const response = await axios.get(`${this.baseUrl}/products.json`, {
        headers: this.headers,
        params: {
          limit: 250, // Maximum allowed
          fields: 'id,title,vendor,product_type,tags,variants'
        }
      });

      // Find the product that contains a variant with the matching SKU
      for (const product of response.data.products) {
        const matchingVariant = product.variants.find(variant => variant.sku === sku);
        if (matchingVariant) {
          console.log(`✅ Found product by SKU: ${product.title}`);
          return {
            success: true,
            product: product,
            variant: matchingVariant,
            vendor: product.vendor,
            source: 'sku_search'
          };
        }
      }

      // If not found in first batch, try GraphQL for more comprehensive search
      return await this.fetchBySKUGraphQL(sku);
      
    } catch (error) {
      console.error(`❌ Error fetching product by SKU ${sku}:`, error.message);
      return {
        success: false,
        error: error.message,
        source: 'sku_search'
      };
    }
  }

  /**
   * Fetch product data by SKU using GraphQL (more efficient for large catalogs)
   * @param {string} sku - Product SKU
   * @returns {Promise<Object>} Product data
   */
  async fetchBySKUGraphQL(sku) {
    try {
      const query = `
        query getProductBySKU($sku: String!) {
          productVariants(first: 1, query: $sku) {
            edges {
              node {
                id
                sku
                title
                price
                inventoryQuantity
                product {
                  id
                  title
                  vendor
                  productType
                  tags
                  handle
                }
              }
            }
          }
        }
      `;

      const response = await axios.post(`${this.baseUrl}/graphql.json`, {
        query: query,
        variables: { sku: `sku:${sku}` }
      }, {
        headers: this.headers
      });

      if (response.data.data.productVariants.edges.length > 0) {
        const variantData = response.data.data.productVariants.edges[0].node;
        console.log(`✅ Found product by SKU (GraphQL): ${variantData.product.title}`);
        
        return {
          success: true,
          product: variantData.product,
          variant: variantData,
          vendor: variantData.product.vendor,
          source: 'graphql_sku_search'
        };
      }

      return {
        success: false,
        error: `Product with SKU ${sku} not found`,
        source: 'graphql_sku_search'
      };

    } catch (error) {
      console.error(`❌ Error fetching product by SKU (GraphQL) ${sku}:`, error.message);
      return {
        success: false,
        error: error.message,
        source: 'graphql_sku_search'
      };
    }
  }

  /**
   * Fetch product data by variant_id
   * @param {number|string} variantId - Shopify variant ID
   * @returns {Promise<Object>} Product data
   */
  async fetchByVariantId(variantId) {
    try {
      console.log(`🔍 Searching for product with variant ID: ${variantId}`);
      
      // First get the variant details
      const variantResponse = await axios.get(`${this.baseUrl}/variants/${variantId}.json`, {
        headers: this.headers
      });

      const variant = variantResponse.data.variant;
      
      // Then get the full product details
      const productResponse = await axios.get(`${this.baseUrl}/products/${variant.product_id}.json`, {
        headers: this.headers
      });

      const product = productResponse.data.product;
      
      console.log(`✅ Found product by variant ID: ${product.title}`);
      
      return {
        success: true,
        product: product,
        variant: variant,
        vendor: product.vendor,
        source: 'variant_id_search'
      };

    } catch (error) {
      console.error(`❌ Error fetching product by variant ID ${variantId}:`, error.message);
      return {
        success: false,
        error: error.message,
        source: 'variant_id_search'
      };
    }
  }

  /**
   * Fetch product data using either SKU or variant_id (smart detection)
   * @param {string|number} identifier - Either SKU (string) or variant_id (number)
   * @returns {Promise<Object>} Product data
   */
  async fetchProduct(identifier) {
    // Determine if identifier is SKU or variant_id
    if (typeof identifier === 'string' && isNaN(identifier)) {
      // It's a SKU
      return await this.fetchBySKU(identifier);
    } else {
      // It's a variant_id (number or numeric string)
      return await this.fetchByVariantId(identifier);
    }
  }

  /**
   * Fetch multiple products by their identifiers
   * @param {Array} identifiers - Array of SKUs or variant_ids
   * @returns {Promise<Array>} Array of product data
   */
  async fetchMultipleProducts(identifiers) {
    const results = [];
    
    for (const identifier of identifiers) {
      const result = await this.fetchProduct(identifier);
      results.push({
        identifier,
        ...result
      });
      
      // Add small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return results;
  }
}

// Example usage
async function testFetcher() {
  const fetcher = new ShopifyProductFetcher();
  
  console.log('🚀 Testing Shopify Product Fetcher...\n');
  
  // Test with SKU
  console.log('--- Testing with SKU ---');
  const skuResult = await fetcher.fetchBySKU('BDDF_SKU5IwW27VcnvavFfOt');
  console.log('SKU Result:', JSON.stringify(skuResult, null, 2));
  
  console.log('\n--- Testing with Variant ID ---');
  // Test with variant_id
  const variantResult = await fetcher.fetchByVariantId(49836981453080);
  console.log('Variant ID Result:', JSON.stringify(variantResult, null, 2));
  
  console.log('\n--- Testing smart detection ---');
  // Test smart detection
  const smartResult1 = await fetcher.fetchProduct('BDDF_SKU5IwW27VcnvavFfOt');
  const smartResult2 = await fetcher.fetchProduct(49836981453080);
  
  console.log('Smart SKU Result:', JSON.stringify(smartResult1, null, 2));
  console.log('Smart Variant ID Result:', JSON.stringify(smartResult2, null, 2));
}

// Export for use in other modules
module.exports = ShopifyProductFetcher;

// Run test if this file is executed directly
if (require.main === module) {
  testFetcher().catch(console.error);
}
