import { Supplier, ISupplier, ISupplierContact, ISupplierAutomation } from '../models/Supplier';
import logger from '../utils/logger';

export interface SupplierCreateData {
  name: string;
  priority?: 'HIGH' | 'MEDIUM' | 'LOW';
  verified?: boolean;
  active?: boolean;
  contact: ISupplierContact;
  returnPolicy?: {
    url?: string;
    timeLimit?: string;
    requirements?: string;
    restockingFee?: string;
    returnAddress?: string;
  };
  products?: {
    count?: number;
    categories?: string[];
    tags?: string[];
  };
  automation?: Partial<ISupplierAutomation>;
  notes?: string;
  tags?: string[];
  vendorNames?: string[];
  shopifyVendorNames?: string[];
}

export interface SupplierUpdateData extends Partial<SupplierCreateData> {
  _id?: string;
}

export class MongoSupplierService {
  /**
   * Create a new supplier
   */
  async createSupplier(supplierData: SupplierCreateData): Promise<ISupplier | null> {
    try {
      const supplier = new Supplier({
        ...supplierData,
        automation: {
          requiresApproval: true,
          autoRefund: false,
          processingTime: '2-3 business days',
          ...supplierData.automation
        }
      });

      const savedSupplier = await supplier.save();
      
      logger.info('Supplier created successfully', {
        supplierId: savedSupplier._id,
        name: savedSupplier.name,
        email: savedSupplier.contact.email
      });

      return savedSupplier;
    } catch (error: any) {
      logger.error('Failed to create supplier', {
        error: error.message,
        supplierName: supplierData.name
      });
      return null;
    }
  }

  /**
   * Update an existing supplier
   */
  async updateSupplier(supplierId: string, updateData: SupplierUpdateData): Promise<ISupplier | null> {
    try {
      const supplier = await Supplier.findByIdAndUpdate(
        supplierId,
        { $set: updateData },
        { new: true, runValidators: true }
      );

      if (!supplier) {
        logger.warn('Supplier not found for update', { supplierId });
        return null;
      }

      logger.info('Supplier updated successfully', {
        supplierId: supplier._id,
        name: supplier.name
      });

      return supplier;
    } catch (error: any) {
      logger.error('Failed to update supplier', {
        error: error.message,
        supplierId
      });
      return null;
    }
  }

  /**
   * Get supplier by ID
   */
  async getSupplierById(supplierId: string): Promise<ISupplier | null> {
    try {
      const supplier = await Supplier.findById(supplierId);
      return supplier;
    } catch (error: any) {
      logger.error('Failed to get supplier by ID', {
        error: error.message,
        supplierId
      });
      return null;
    }
  }

  /**
   * Get supplier by vendor name (supports multiple name variations)
   */
  async getSupplierByVendorName(vendorName: string): Promise<ISupplier | null> {
    try {
      const suppliers = await Supplier.findByVendorName(vendorName);
      
      if (suppliers.length === 0) {
        logger.debug('No supplier found for vendor name', { vendorName });
        return null;
      }

      if (suppliers.length > 1) {
        logger.warn('Multiple suppliers found for vendor name, returning first', {
          vendorName,
          count: suppliers.length
        });
      }

      return suppliers[0];
    } catch (error: any) {
      logger.error('Failed to get supplier by vendor name', {
        error: error.message,
        vendorName
      });
      return null;
    }
  }

  /**
   * Get suppliers by tags
   */
  async getSuppliersByTags(tags: string[]): Promise<ISupplier[]> {
    try {
      const suppliers = await Supplier.findByTags(tags);
      return suppliers;
    } catch (error: any) {
      logger.error('Failed to get suppliers by tags', {
        error: error.message,
        tags
      });
      return [];
    }
  }

  /**
   * Get all active suppliers
   */
  async getAllActiveSuppliers(): Promise<ISupplier[]> {
    try {
      const suppliers = await Supplier.findActiveSuppliers();
      return suppliers;
    } catch (error: any) {
      logger.error('Failed to get all active suppliers', {
        error: error.message
      });
      return [];
    }
  }

  /**
   * Get suppliers by priority
   */
  async getSuppliersByPriority(priority: 'HIGH' | 'MEDIUM' | 'LOW'): Promise<ISupplier[]> {
    try {
      const suppliers = await Supplier.find({ priority, active: true }).sort({ name: 1 });
      return suppliers;
    } catch (error: any) {
      logger.error('Failed to get suppliers by priority', {
        error: error.message,
        priority
      });
      return [];
    }
  }

  /**
   * Search suppliers by text
   */
  async searchSuppliers(searchTerm: string): Promise<ISupplier[]> {
    try {
      const suppliers = await Supplier.find({
        $text: { $search: searchTerm },
        active: true
      }).sort({ score: { $meta: 'textScore' } });

      return suppliers;
    } catch (error: any) {
      logger.error('Failed to search suppliers', {
        error: error.message,
        searchTerm
      });
      return [];
    }
  }

  /**
   * Delete supplier (soft delete by setting active to false)
   */
  async deleteSupplier(supplierId: string): Promise<boolean> {
    try {
      const supplier = await Supplier.findByIdAndUpdate(
        supplierId,
        { $set: { active: false } },
        { new: true }
      );

      if (!supplier) {
        logger.warn('Supplier not found for deletion', { supplierId });
        return false;
      }

      logger.info('Supplier soft deleted successfully', {
        supplierId: supplier._id,
        name: supplier.name
      });

      return true;
    } catch (error: any) {
      logger.error('Failed to delete supplier', {
        error: error.message,
        supplierId
      });
      return false;
    }
  }

  /**
   * Get supplier statistics
   */
  async getSupplierStats(): Promise<{
    total: number;
    active: number;
    verified: number;
    byPriority: { HIGH: number; MEDIUM: number; LOW: number };
  }> {
    try {
      const [total, active, verified, highPriority, mediumPriority, lowPriority] = await Promise.all([
        Supplier.countDocuments({}),
        Supplier.countDocuments({ active: true }),
        Supplier.countDocuments({ verified: true, active: true }),
        Supplier.countDocuments({ priority: 'HIGH', active: true }),
        Supplier.countDocuments({ priority: 'MEDIUM', active: true }),
        Supplier.countDocuments({ priority: 'LOW', active: true })
      ]);

      return {
        total,
        active,
        verified,
        byPriority: {
          HIGH: highPriority,
          MEDIUM: mediumPriority,
          LOW: lowPriority
        }
      };
    } catch (error: any) {
      logger.error('Failed to get supplier statistics', {
        error: error.message
      });
      return {
        total: 0,
        active: 0,
        verified: 0,
        byPriority: { HIGH: 0, MEDIUM: 0, LOW: 0 }
      };
    }
  }

  /**
   * Bulk import suppliers from JSON data (migration helper)
   */
  async bulkImportSuppliers(suppliersData: SupplierCreateData[]): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const supplierData of suppliersData) {
      try {
        await this.createSupplier(supplierData);
        success++;
      } catch (error: any) {
        failed++;
        errors.push(`${supplierData.name}: ${error.message}`);
      }
    }

    logger.info('Bulk import completed', { success, failed, total: suppliersData.length });

    return { success, failed, errors };
  }
}
