const { ShopifyService } = require('./src/services/ShopifyService');

/**
 * Test script to demonstrate fetching Shopify product data by SKU or variant_id
 */
async function testShopifyFetch() {
  console.log('🚀 Testing Shopify Product Fetching...\n');
  
  const shopifyService = new ShopifyService();
  
  // Test data from the webhook
  const testSKU = 'BDDF_SKU5IwW27VcnvavFfOt';
  const testVariantId = 49836981453080;
  
  try {
    // Test 1: Fetch by SKU
    console.log('--- Test 1: Fetch by SKU ---');
    console.log(`Searching for SKU: ${testSKU}`);
    
    const skuResult = await shopifyService.fetchProductBySKU(testSKU);
    console.log('SKU Result:', JSON.stringify(skuResult, null, 2));
    
    if (skuResult.success) {
      console.log(`✅ Found product: ${skuResult.product.title}`);
      console.log(`   Vendor: ${skuResult.vendor}`);
      console.log(`   Variant ID: ${skuResult.variant.id}`);
      console.log(`   Price: $${skuResult.variant.price}`);
    } else {
      console.log(`❌ Failed to find product by SKU: ${skuResult.error}`);
    }
    
    console.log('\n--- Test 2: Fetch by Variant ID ---');
    console.log(`Searching for Variant ID: ${testVariantId}`);
    
    const variantResult = await shopifyService.fetchProductByVariantId(testVariantId);
    console.log('Variant ID Result:', JSON.stringify(variantResult, null, 2));
    
    if (variantResult.success) {
      console.log(`✅ Found product: ${variantResult.product.title}`);
      console.log(`   Vendor: ${variantResult.vendor}`);
      console.log(`   SKU: ${variantResult.variant.sku}`);
      console.log(`   Price: $${variantResult.variant.price}`);
    } else {
      console.log(`❌ Failed to find product by Variant ID: ${variantResult.error}`);
    }
    
    console.log('\n--- Test 3: Smart Detection ---');
    
    // Test smart detection with SKU
    console.log(`Smart fetch with SKU: ${testSKU}`);
    const smartSKUResult = await shopifyService.fetchProductData(testSKU);
    console.log(`Result: ${smartSKUResult.success ? '✅ Success' : '❌ Failed'} (Source: ${smartSKUResult.source})`);
    
    // Test smart detection with Variant ID
    console.log(`Smart fetch with Variant ID: ${testVariantId}`);
    const smartVariantResult = await shopifyService.fetchProductData(testVariantId);
    console.log(`Result: ${smartVariantResult.success ? '✅ Success' : '❌ Failed'} (Source: ${smartVariantResult.source})`);
    
    console.log('\n--- Test 4: Batch Processing ---');
    
    // Test multiple identifiers
    const identifiers = [testSKU, testVariantId, 'INVALID_SKU', 999999999];
    
    for (const identifier of identifiers) {
      console.log(`\nFetching: ${identifier}`);
      const result = await shopifyService.fetchProductData(identifier);
      
      if (result.success) {
        console.log(`✅ Success: ${result.product.title} (${result.vendor})`);
      } else {
        console.log(`❌ Failed: ${result.error}`);
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

/**
 * Test webhook data processing
 */
async function testWebhookDataProcessing() {
  console.log('\n🔄 Testing Webhook Data Processing...\n');
  
  const shopifyService = new ShopifyService();
  
  // Simulate webhook line item data
  const webhookLineItem = {
    "id": 15881499509016,
    "original_product": {
      "price": 64.99,
      "product_id": 9678670397720,
      "sku": "BDDF_SKU5IwW27VcnvavFfOt",
      "title": "Grav Labs 6 inch Upright Bubbler",
      "variant_id": 49836981453080,
      "variant_title": null
    },
    "quantity": 1,
    "reason": "Others"
  };
  
  console.log('Processing webhook line item:');
  console.log(JSON.stringify(webhookLineItem, null, 2));
  
  try {
    // Try fetching by SKU first
    let result = await shopifyService.fetchProductData(webhookLineItem.original_product.sku);
    
    if (!result.success && webhookLineItem.original_product.variant_id) {
      // Fallback to variant_id if SKU fails
      console.log('SKU lookup failed, trying variant_id...');
      result = await shopifyService.fetchProductData(webhookLineItem.original_product.variant_id);
    }
    
    if (result.success) {
      console.log('\n✅ Successfully processed webhook item:');
      console.log(`   Product: ${result.product.title}`);
      console.log(`   Vendor: ${result.vendor}`);
      console.log(`   SKU: ${result.variant.sku}`);
      console.log(`   Variant ID: ${result.variant.id}`);
      console.log(`   Source: ${result.source}`);
      
      // This is the vendor information you can use for notifications
      console.log(`\n📧 Vendor for notifications: ${result.vendor}`);
      
    } else {
      console.log('❌ Failed to process webhook item:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Webhook processing failed:', error.message);
  }
}

// Run tests
async function runAllTests() {
  await testShopifyFetch();
  await testWebhookDataProcessing();
  console.log('\n🎉 All tests completed!');
}

// Export for use in other modules
module.exports = {
  testShopifyFetch,
  testWebhookDataProcessing,
  runAllTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
