import express, { Request, Response } from 'express';
import RenderApiService from '../services/RenderApiService';
import logger from '../utils/logger';
import { addLogEntry } from './logs';

const router = express.Router();


let renderService: RenderApiService | null = null;

function initializeRenderService(): RenderApiService | null {
  const apiKey = process.env.RENDER_API_KEY;
  
  if (!apiKey) {
    logger.warn('RENDER_API_KEY not found in environment variables');
    return null;
  }

  try {
    renderService = new RenderApiService({ apiKey });
    logger.info('Render API service initialized successfully');
    return renderService;
  } catch (error: any) {
    logger.error('Failed to initialize Render API service', { error: error.message });
    return null;
  }
}

// Middleware to ensure Render service is available
const ensureRenderService = (req: Request, res: Response, next: any) => {
  if (!renderService) {
    renderService = initializeRenderService();
  }

  if (!renderService) {
    return res.status(503).json({
      success: false,
      error: 'Render API service not available. Please check your RENDER_API_KEY environment variable.'
    });
  }

  next();
};

// Test Render API connection
router.get('/test-connection', async (req: Request, res: Response) => {
  try {
    if (!renderService) {
      renderService = initializeRenderService();
    }

    if (!renderService) {
      return res.status(503).json({
        success: false,
        error: 'Render API service not available'
      });
    }

    const isConnected = await renderService.testConnection();
    
    if (isConnected) {
      addLogEntry('success', 'Render API connection test successful');
      res.json({
        success: true,
        message: 'Successfully connected to Render API'
      });
    } else {
      addLogEntry('error', 'Render API connection test failed');
      res.status(500).json({
        success: false,
        error: 'Failed to connect to Render API'
      });
    }
  } catch (error: any) {
    logger.error('Error testing Render connection', { error: error.message });
    addLogEntry('error', 'Render API connection test error', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get all Render services
router.get('/services', ensureRenderService, async (req: Request, res: Response) => {
  try {
    const services = await renderService!.getServices();
    
    addLogEntry('info', `Fetched ${services.length} Render services`);
    
    res.json({
      success: true,
      data: services,
      count: services.length
    });
  } catch (error: any) {
    logger.error('Error fetching Render services', { error: error.message });
    addLogEntry('error', 'Failed to fetch Render services', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get specific service by ID
router.get('/services/:serviceId', ensureRenderService, async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params;
    const service = await renderService!.getService(serviceId);
    
    if (!service) {
      return res.status(404).json({
        success: false,
        error: 'Service not found'
      });
    }

    addLogEntry('info', `Fetched Render service: ${service.name}`, { serviceId });
    
    res.json({
      success: true,
      data: service
    });
  } catch (error: any) {
    logger.error('Error fetching Render service', { error: error.message });
    addLogEntry('error', 'Failed to fetch Render service', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get logs for all services or specific services
router.get('/logs', ensureRenderService, async (req: Request, res: Response) => {
  try {
    const {
      resourceIds,
      startTime,
      endTime,
      limit = 100,
      level,
      search
    } = req.query;

    const params: any = {
      limit: parseInt(limit as string)
    };

    if (resourceIds) {
      params.resourceIds = Array.isArray(resourceIds) ? resourceIds : [resourceIds];
    }

    // Set default time range if not provided (last hour)
    if (!startTime && !endTime) {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      params.startTime = oneHourAgo.toISOString();
      params.endTime = now.toISOString();
    } else {
      if (startTime) params.startTime = startTime as string;
      if (endTime) params.endTime = endTime as string;
    }

    if (level) params.level = level as string;
    if (search) params.search = search as string;

    logger.info('Fetching logs with params', params);

    const logsResponse = await renderService!.getLogs(params);

    addLogEntry('info', `Fetched ${logsResponse.logs?.length || 0} Render logs`);

    res.json({
      success: true,
      data: logsResponse
    });
  } catch (error: any) {
    logger.error('Error fetching Render logs', { error: error.message });
    addLogEntry('error', 'Failed to fetch Render logs', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get logs for specific service
router.get('/services/:serviceId/logs', ensureRenderService, async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params;
    const {
      startTime,
      endTime,
      limit = 100,
      level,
      search
    } = req.query;

    const params: any = {
      limit: parseInt(limit as string)
    };

    // Set default time range if not provided (last hour)
    if (!startTime && !endTime) {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      params.startTime = oneHourAgo.toISOString();
      params.endTime = now.toISOString();
    } else {
      if (startTime) params.startTime = startTime as string;
      if (endTime) params.endTime = endTime as string;
    }

    if (level) params.level = level as string;
    if (search) params.search = search as string;

    const logsResponse = await renderService!.getServiceLogs(serviceId, params);

    addLogEntry('info', `Fetched ${logsResponse.logs?.length || 0} logs for service ${serviceId}`);

    res.json({
      success: true,
      data: logsResponse
    });
  } catch (error: any) {
    logger.error('Error fetching service logs', { serviceId: req.params.serviceId, error: error.message });
    addLogEntry('error', 'Failed to fetch service logs', { serviceId: req.params.serviceId, error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update API key
router.post('/update-api-key', async (req: Request, res: Response) => {
  try {
    const { apiKey } = req.body;
    
    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: 'API key is required'
      });
    }

    // Create new service instance with updated key
    renderService = new RenderApiService({ apiKey });
    
    // Test the connection
    const isConnected = await renderService.testConnection();
    
    if (isConnected) {
      addLogEntry('success', 'Render API key updated successfully');
      res.json({
        success: true,
        message: 'API key updated and connection verified'
      });
    } else {
      addLogEntry('error', 'Invalid Render API key provided');
      renderService = null;
      res.status(400).json({
        success: false,
        error: 'Invalid API key provided'
      });
    }
  } catch (error: any) {
    logger.error('Error updating Render API key', { error: error.message });
    addLogEntry('error', 'Failed to update Render API key', { error: error.message });
    renderService = null;
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Initialize service on startup
initializeRenderService();

export default router;
