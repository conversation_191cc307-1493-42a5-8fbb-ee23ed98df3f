import { Router, Request, Response } from 'express';
import { ReturnPrimeWebhookPayload, WebhookResponse } from '../types';
import { captureRawBody, verifySignature, validatePayload } from '../middleware/webhook';
import { EnhancedReturnPrimeService } from '../services/EnhancedReturnPrimeService';
import { ShopifyService } from '../services/ShopifyService';
import { generateIdempotencyKey } from '../utils/security';
import { transformReturnPrimePayload, getEventTypeFromHeaders } from '../utils/transform';
import logger from '../utils/logger';

const router = Router();
const enhancedReturnService = new EnhancedReturnPrimeService();
const shopifyService = new ShopifyService();

/**
 * Health check endpoint
 */
router.get('/health', (_req: Request, res: Response) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

/**
 * Enhanced return service test endpoint
 */
router.get('/test/enhanced-service', async (_req: Request, res: Response) => {
  try {
    res.json({
      service: 'Enhanced Return Processing Service',
      status: 'active',
      supportedVendors: ['Buddify', 'SmokeDrop'],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Enhanced service test failed', { error });
    res.status(500).json({
      service: 'Enhanced Return Processing Service',
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get return details from Return Prime API (DISABLED - using enhanced service only)
 */
router.get('/return-prime/return/:returnId', async (req: Request, res: Response) => {
  res.status(501).json({
    error: 'Endpoint disabled - using enhanced service only',
    message: 'This endpoint has been replaced by the enhanced return processing service'
  });
});

/**
 * Get order details from Return Prime API (DISABLED - using enhanced service only)
 */
router.get('/return-prime/order/:orderNumber', async (req: Request, res: Response) => {
  res.status(501).json({
    error: 'Endpoint disabled - using enhanced service only',
    message: 'This endpoint has been replaced by the enhanced return processing service'
  });
});

/**
 * Simple logging endpoint for Return Prime webhooks (for debugging)
 */
router.post('/webhook/return-prime-log',
  captureRawBody,
  async (req: Request, res: Response) => {
    try {
      let payload;

      // Parse JSON if body is string
      if (typeof req.body === 'string') {
        try {
          payload = JSON.parse(req.body);
        } catch (parseError) {
          payload = req.body;
        }
      } else {
        payload = req.body;
      }

      // Log everything for debugging
      logger.info('=== RETURN PRIME WEBHOOK DATA ===', {
        headers: req.headers,
        payload: payload,
        rawBody: req.rawBody?.toString(),
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Pretty print the JSON to console for easy reading
      console.log('\n=== RETURN PRIME WEBHOOK JSON ===');
      console.log('Headers:', JSON.stringify(req.headers, null, 2));
      console.log('Payload:', JSON.stringify(payload, null, 2));
      console.log('=== END WEBHOOK DATA ===\n');

      // Always respond with success
      res.json({
        success: true,
        message: 'Webhook data logged successfully',
        timestamp: new Date().toISOString(),
        receivedHeaders: Object.keys(req.headers),
        payloadType: typeof payload,
        payloadSize: req.rawBody?.length || 0
      });

    } catch (error) {
      logger.error('Error logging webhook data', { error });
      res.status(500).json({
        success: false,
        error: 'Failed to log webhook data',
        timestamp: new Date().toISOString()
      });
    }
  }
);

/**
 * Main webhook endpoint for Return Prime events with Shopify integration
 */
router.post('/webhook/return-prime',
  captureRawBody,
  // Temporarily disable signature verification for testing
  // verifySignature,
  validatePayload,
  async (req: Request, res: Response) => {
    const rawPayload: ReturnPrimeWebhookPayload = req.body;

    // Transform Return Prime format to our internal format
    const payload = transformReturnPrimePayload(rawPayload);
    const eventType = getEventTypeFromHeaders(req.headers);

    const idempotencyKey = generateIdempotencyKey(
      payload.return_id,
      eventType,
      payload.created_at
    );

    logger.info('Received Return Prime webhook', {
      returnId: payload.return_id,
      eventType: eventType,
      orderId: payload.order_id,
      itemCount: payload.items.length,
      idempotencyKey,
      originalStatus: rawPayload.request.status
    });

    try {

      // Use enhanced processing for all return events (Buddify and SmokeDrop only)
      logger.info('🚀 UPDATED: Processing ALL return events with enhanced service (Buddify/SmokeDrop only)', {
        returnId: payload.return_id,
        eventType,
        idempotencyKey,
        version: 'v2.0-updated',
        supportedVendors: ['Buddify', 'SmokeDrop']
      });

      const result = await enhancedReturnService.processRefundWebhook(rawPayload, req.headers);

      if (result.success) {
        logger.info('Return processed successfully with enhanced service', {
          returnId: payload.return_id,
          processedItems: result.processedReturn?.items?.length || 0,
          idempotencyKey
        });

        res.json(result);
      } else {
        logger.error('Failed to process return with enhanced service', {
          returnId: payload.return_id,
          error: result.error,
          idempotencyKey
        });

        res.status(500).json(result);
      }
      
    } catch (error) {
      logger.error('Unexpected error processing webhook', {
        error,
        returnId: payload.return_id,
        idempotencyKey
      });

      const response: WebhookResponse = {
        success: false,
        message: 'Internal server error',
        errors: [{
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred',
          retryable: true
        }]
      };

      res.status(500).json(response);
    }
  }
);

/**
 * Raw data capture endpoint (accepts anything)
 */
router.post('/webhook/capture',
  captureRawBody,
  (req: Request, res: Response) => {
    try {
      let payload;

      if (typeof req.body === 'string') {
        try {
          payload = JSON.parse(req.body);
        } catch (parseError) {
          payload = req.body;
        }
      } else {
        payload = req.body;
      }

      console.log('\n🔥 RAW WEBHOOK CAPTURE 🔥');
      console.log('Timestamp:', new Date().toISOString());
      console.log('Method:', req.method);
      console.log('URL:', req.url);
      console.log('IP:', req.ip);
      console.log('User-Agent:', req.get('User-Agent'));
      console.log('\nHeaders:');
      console.log(JSON.stringify(req.headers, null, 2));
      console.log('\nPayload:');
      console.log(JSON.stringify(payload, null, 2));
      console.log('\nRaw Body:');
      console.log(req.rawBody?.toString());
      console.log('🔥 END CAPTURE 🔥\n');

      res.json({
        success: true,
        message: 'Data captured and logged',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in capture endpoint:', error);
      res.status(500).json({ error: 'Capture failed' });
    }
  }
);

/**
 * Test endpoint for webhook validation (development only)
 */
if (process.env.NODE_ENV !== 'production') {
  router.post('/webhook/test', (req: Request, res: Response) => {
    logger.info('Test webhook received', { body: req.body });
    res.json({ message: 'Test webhook received successfully' });
  });

  /**
   * Debug endpoint to test Return Prime signature verification
   */
  router.post('/webhook/debug', captureRawBody, (req: Request, res: Response) => {
    const signature = req.headers['x-rp-hmac-sha512'] as string;
    const payload = req.rawBody?.toString() || '';

    logger.info('Debug webhook received', {
      headers: req.headers,
      signature,
      payloadLength: payload.length,
      payload: payload.substring(0, 200) + '...' // Log first 200 chars
    });

    // Test signature verification with different methods
    const crypto = require('crypto');
    const secret = process.env.RETURN_PRIME_WEBHOOK_SECRET || 'test_webhook_secret_123';

    // Method 1: SHA512 HMAC
    const hmac512 = crypto.createHmac('sha512', secret).update(payload, 'utf8').digest('hex');

    // Method 2: SHA256 HMAC
    const hmac256 = crypto.createHmac('sha256', secret).update(payload, 'utf8').digest('hex');

    // Method 3: Base64 encoded
    const hmac512Base64 = crypto.createHmac('sha512', secret).update(payload, 'utf8').digest('base64');

    res.json({
      received_signature: signature,
      computed_sha512: hmac512,
      computed_sha256: hmac256,
      computed_sha512_base64: hmac512Base64,
      signature_matches_sha512: signature === hmac512,
      signature_matches_sha256: signature === hmac256,
      signature_matches_sha512_base64: signature === hmac512Base64,
      payload_length: payload.length,
      secret_used: secret
    });
  });
}

// ===== SHOPIFY API ROUTES =====

/**
 * Get product data by SKU
 * GET /api/shopify/product/sku/:sku
 */
router.get('/api/shopify/product/sku/:sku', async (req: Request, res: Response) => {
  try {
    const { sku } = req.params;

    if (!sku) {
      return res.status(400).json({
        success: false,
        error: 'SKU parameter is required'
      });
    }

    logger.info('Fetching product by SKU', { sku });

    const result = await shopifyService.fetchProductBySKU(sku);

    if (result.success) {
      res.json({
        success: true,
        data: {
          product: result.product,
          variant: result.variant,
          vendor: result.vendor,
          tags: result.product?.tags || [],
          source: result.source
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error || 'Product not found'
      });
    }
  } catch (error: any) {
    logger.error('Error fetching product by SKU', { error: error.message, sku: req.params.sku });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Get product data by Variant ID
 * GET /api/shopify/product/variant/:variantId
 */
router.get('/api/shopify/product/variant/:variantId', async (req: Request, res: Response) => {
  try {
    const { variantId } = req.params;

    if (!variantId || isNaN(Number(variantId))) {
      return res.status(400).json({
        success: false,
        error: 'Valid variant ID parameter is required'
      });
    }

    logger.info('Fetching product by variant ID', { variantId });

    const result = await shopifyService.fetchProductByVariantId(Number(variantId));

    if (result.success) {
      res.json({
        success: true,
        data: {
          product: result.product,
          variant: result.variant,
          vendor: result.vendor,
          tags: result.product?.tags || [],
          source: result.source
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error || 'Product not found'
      });
    }
  } catch (error: any) {
    logger.error('Error fetching product by variant ID', { error: error.message, variantId: req.params.variantId });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Get detailed product information by Product ID
 * GET /api/shopify/product/:productId
 */
router.get('/api/shopify/product/:productId', async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;

    if (!productId) {
      return res.status(400).json({
        success: false,
        error: 'Product ID parameter is required'
      });
    }

    logger.info('Fetching detailed product information', { productId });

    const result = await shopifyService.getProductDetails(productId);

    if (result.success) {
      res.json({
        success: true,
        data: {
          product: result.product,
          vendor: result.vendor,
          tags: result.tags || [],
          metafields: result.metafields || [],
          vendor_details: result.vendor_details
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error || 'Product not found'
      });
    }
  } catch (error: any) {
    logger.error('Error fetching product details', { error: error.message, productId: req.params.productId });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Smart product lookup - automatically detects SKU or Variant ID
 * GET /api/shopify/product/lookup/:identifier
 */
router.get('/api/shopify/product/lookup/:identifier', async (req: Request, res: Response) => {
  try {
    const { identifier } = req.params;

    if (!identifier) {
      return res.status(400).json({
        success: false,
        error: 'Identifier parameter is required'
      });
    }

    logger.info('Smart product lookup', { identifier });

    const result = await shopifyService.fetchProductData(identifier);

    if (result.success) {
      res.json({
        success: true,
        data: {
          product: result.product,
          variant: result.variant,
          vendor: result.vendor,
          tags: result.product?.tags || [],
          source: result.source,
          identifier_type: typeof identifier === 'string' && isNaN(Number(identifier)) ? 'SKU' : 'Variant ID'
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error || 'Product not found'
      });
    }
  } catch (error: any) {
    logger.error('Error in smart product lookup', { error: error.message, identifier: req.params.identifier });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Get order details from Shopify
 * GET /api/shopify/order/:orderId
 */
router.get('/api/shopify/order/:orderId', async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        error: 'Order ID parameter is required'
      });
    }

    logger.info('Fetching order details', { orderId });

    const result = await shopifyService.getOrderDetails(orderId);

    if (result.success) {
      res.json({
        success: true,
        data: {
          order: result.order
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: result.error || 'Order not found'
      });
    }
  } catch (error: any) {
    logger.error('Error fetching order details', { error: error.message, orderId: req.params.orderId });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Search products by multiple criteria
 * POST /api/shopify/products/search
 */
router.post('/api/shopify/products/search', async (req: Request, res: Response) => {
  try {
    const { skus, variantIds, productIds } = req.body;

    if (!skus && !variantIds && !productIds) {
      return res.status(400).json({
        success: false,
        error: 'At least one search criteria (skus, variantIds, or productIds) is required'
      });
    }

    logger.info('Bulk product search', {
      skuCount: skus?.length || 0,
      variantIdCount: variantIds?.length || 0,
      productIdCount: productIds?.length || 0
    });

    const results = [];

    // Search by SKUs
    if (skus && Array.isArray(skus)) {
      for (const sku of skus) {
        const result = await shopifyService.fetchProductBySKU(sku);
        results.push({
          identifier: sku,
          type: 'SKU',
          ...result
        });
      }
    }

    // Search by Variant IDs
    if (variantIds && Array.isArray(variantIds)) {
      for (const variantId of variantIds) {
        const result = await shopifyService.fetchProductByVariantId(Number(variantId));
        results.push({
          identifier: variantId,
          type: 'Variant ID',
          ...result
        });
      }
    }

    // Search by Product IDs
    if (productIds && Array.isArray(productIds)) {
      for (const productId of productIds) {
        const result = await shopifyService.getProductDetails(productId);
        results.push({
          identifier: productId,
          type: 'Product ID',
          ...result
        });
      }
    }

    res.json({
      success: true,
      data: {
        results,
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    });

  } catch (error: any) {
    logger.error('Error in bulk product search', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
