import { validateWebhookPayload } from '../utils/validation';
import { SupplierMapper } from '../services/SupplierMapper';
import { IdempotencyService } from '../services/IdempotencyService';

describe('Webhook Processing', () => {
  describe('Payload Validation', () => {
    it('should validate a correct webhook payload', () => {
      const payload = {
        event_type: 'request.created',
        return_id: 'RET-12345',
        order_id: '1234567890',
        customer_email: '<EMAIL>',
        customer_name: '<PERSON>',
        items: [
          {
            sku: 'BUDDIFY-PRODUCT-001',
            name: 'Test Product',
            qty: 1,
            reason: 'Defective',
            images: ['https://example.com/image1.jpg'],
            vendor_name: 'Buddify'
          }
        ],
        created_at: '2023-12-01T10:00:00Z',
        status: 'pending',
        exchange: false
      };

      const result = validateWebhookPayload(payload);
      expect(result.error).toBeUndefined();
      expect(result.value).toBeDefined();
    });

    it('should reject invalid webhook payload', () => {
      const payload = {
        event_type: 'invalid_event',
        return_id: 'RET-12345',
        // Missing required fields
      };

      const result = validateWebhookPayload(payload);
      expect(result.error).toBeDefined();
      expect(result.value).toBeUndefined();
    });
  });

  describe('Supplier Mapping', () => {
    let supplierMapper: SupplierMapper;

    beforeEach(() => {
      supplierMapper = new SupplierMapper();
    });

    it('should map item to supplier by SKU prefix', () => {
      const item = {
        sku: 'BUDDIFY-PRODUCT-001',
        name: 'Test Product',
        qty: 1,
        reason: 'Defective'
      };

      const supplier = supplierMapper.mapItemToSupplier(item);
      expect(supplier).toBe('Buddify');
    });

    it('should map item to supplier by vendor name', () => {
      const item = {
        sku: 'UNKNOWN-SKU',
        name: 'Test Product',
        qty: 1,
        reason: 'Defective',
        vendor_name: 'SmokeDrop'
      };

      const supplier = supplierMapper.mapItemToSupplier(item);
      expect(supplier).toBe('SmokeDrop');
    });

    it('should return Unknown for unmappable items', () => {
      const item = {
        sku: 'UNKNOWN-SKU',
        name: 'Test Product',
        qty: 1,
        reason: 'Defective'
      };

      const supplier = supplierMapper.mapItemToSupplier(item);
      expect(supplier).toBe('Unknown');
    });

    it('should group items by supplier correctly', () => {
      const items = [
        {
          sku: 'BUDDIFY-PRODUCT-001',
          name: 'Buddify Product',
          qty: 1,
          reason: 'Defective'
        },
        {
          sku: 'SMOKEDROP-PRODUCT-001',
          name: 'SmokeDrop Product',
          qty: 2,
          reason: 'Wrong item'
        },
        {
          sku: 'BUDDIFY-PRODUCT-002',
          name: 'Another Buddify Product',
          qty: 1,
          reason: 'Damaged'
        }
      ];

      const groups = supplierMapper.groupItemsBySupplier(items);
      
      expect(groups.has('Buddify')).toBe(true);
      expect(groups.has('SmokeDrop')).toBe(true);
      expect(groups.get('Buddify')).toHaveLength(2);
      expect(groups.get('SmokeDrop')).toHaveLength(1);
    });
  });

  describe('Idempotency Service', () => {
    let idempotencyService: IdempotencyService;

    beforeEach(() => {
      idempotencyService = new IdempotencyService();
    });

    afterEach(async () => {
      await idempotencyService.close();
    });

    it('should mark and check processed webhooks', async () => {
      const key = 'test-webhook-123';
      
      // Initially not processed
      const notProcessed = await idempotencyService.checkProcessed(key);
      expect(notProcessed).toBe(false);
      
      // Mark as processed
      const marked = await idempotencyService.markProcessed(key, { test: 'data' });
      expect(marked).toBe(true);
      
      // Should now be processed
      const processed = await idempotencyService.checkProcessed(key);
      expect(processed).toBe(true);
      
      // Should return the stored data
      const result = await idempotencyService.getProcessingResult(key);
      expect(result).toEqual({ test: 'data' });
    });

    it('should handle retry attempts', async () => {
      const key = 'test-retry-123';
      
      // Initially no retry attempts
      const initialAttempts = await idempotencyService.getRetryAttempt(key);
      expect(initialAttempts).toBe(0);
      
      // Store retry attempt
      const stored = await idempotencyService.storeRetryAttempt(key, 1, 'Test error');
      expect(stored).toBe(true);
      
      // Should return the attempt count
      const attempts = await idempotencyService.getRetryAttempt(key);
      expect(attempts).toBe(1);
      
      // Clear retry attempts
      const cleared = await idempotencyService.clearRetryAttempts(key);
      expect(cleared).toBe(true);
      
      // Should be back to 0
      const clearedAttempts = await idempotencyService.getRetryAttempt(key);
      expect(clearedAttempts).toBe(0);
    });

    it('should pass connection test', async () => {
      const testResult = await idempotencyService.testConnection();
      expect(testResult).toBe(true);
    });
  });
});
