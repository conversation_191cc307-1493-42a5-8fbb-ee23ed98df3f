const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DB_NAME = process.env.MONGODB_DB_NAME || 'shopify_automation';

// Supplier Schema (simplified for migration)
const SupplierSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  priority: { type: String, enum: ['HIGH', 'MEDIUM', 'LOW'], default: 'MEDIUM' },
  verified: { type: Boolean, default: false },
  active: { type: Boolean, default: true },
  contact: {
    email: { type: String, required: true },
    phone: String,
    website: String,
    address: {
      street: String,
      city: String,
      state: String,
      zip: String,
      country: String
    },
    businessHours: String,
    timezone: String
  },
  returnPolicy: {
    url: String,
    timeLimit: String,
    requirements: String,
    restockingFee: String,
    returnAddress: String
  },
  products: {
    count: Number,
    categories: [String],
    tags: [String]
  },
  automation: {
    emailTemplate: String,
    requiresApproval: { type: Boolean, default: true },
    autoRefund: { type: Boolean, default: false },
    processingTime: { type: String, default: '2-3 business days' }
  },
  notes: String,
  tags: [String],
  vendorNames: [String],
  shopifyVendorNames: [String]
}, { timestamps: true });

const Supplier = mongoose.model('Supplier', SupplierSchema);

async function migrateVendorsToMongoDB() {
  try {
    console.log('🚀 Starting vendor migration to MongoDB...\n');

    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(`${MONGODB_URI}/${DB_NAME}`);
    console.log('✅ Connected to MongoDB successfully\n');

    // Load existing vendor database
    const vendorDbPath = path.join(__dirname, 'vendor-database.json');
    
    if (!fs.existsSync(vendorDbPath)) {
      console.log('⚠️  vendor-database.json not found. Creating sample suppliers...\n');
      await createSampleSuppliers();
      return;
    }

    console.log('📖 Loading vendor database from JSON...');
    const vendorData = JSON.parse(fs.readFileSync(vendorDbPath, 'utf8'));
    const vendors = vendorData.vendors || {};
    
    console.log(`📊 Found ${Object.keys(vendors).length} vendors in JSON database\n`);

    // Clear existing suppliers
    console.log('🗑️  Clearing existing suppliers...');
    await Supplier.deleteMany({});
    console.log('✅ Existing suppliers cleared\n');

    // Migrate each vendor
    let successCount = 0;
    let failCount = 0;
    const errors = [];

    for (const [vendorName, vendorInfo] of Object.entries(vendors)) {
      try {
        console.log(`📝 Migrating: ${vendorName}`);
        
        const supplierData = {
          name: vendorInfo.name || vendorName,
          priority: vendorInfo.priority || 'MEDIUM',
          verified: vendorInfo.verified || false,
          active: true,
          contact: {
            email: vendorInfo.contact?.email || `contact@${vendorName.toLowerCase().replace(/\s+/g, '')}.com`,
            phone: vendorInfo.contact?.phone,
            website: vendorInfo.contact?.website,
            address: vendorInfo.contact?.address,
            businessHours: vendorInfo.contact?.businessHours,
            timezone: vendorInfo.contact?.timezone
          },
          returnPolicy: vendorInfo.returnPolicy || {},
          products: vendorInfo.products || {},
          automation: {
            emailTemplate: vendorInfo.automation?.emailTemplate,
            requiresApproval: vendorInfo.automation?.requiresApproval !== false,
            autoRefund: vendorInfo.automation?.autoRefund || false,
            processingTime: vendorInfo.automation?.processingTime || '2-3 business days'
          },
          notes: vendorInfo.notes,
          tags: vendorInfo.products?.tags || [],
          vendorNames: [vendorName],
          shopifyVendorNames: [vendorName, vendorInfo.name].filter(Boolean)
        };

        const supplier = new Supplier(supplierData);
        await supplier.save();
        
        console.log(`   ✅ Successfully migrated: ${vendorName}`);
        successCount++;
      } catch (error) {
        console.log(`   ❌ Failed to migrate: ${vendorName} - ${error.message}`);
        errors.push(`${vendorName}: ${error.message}`);
        failCount++;
      }
    }

    // Add additional sample suppliers if needed
    await addAdditionalSuppliers();

    // Summary
    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successfully migrated: ${successCount}`);
    console.log(`❌ Failed to migrate: ${failCount}`);
    
    if (errors.length > 0) {
      console.log('\n❌ Errors:');
      errors.forEach(error => console.log(`   - ${error}`));
    }

    // Display final stats
    const totalSuppliers = await Supplier.countDocuments({});
    const activeSuppliers = await Supplier.countDocuments({ active: true });
    const verifiedSuppliers = await Supplier.countDocuments({ verified: true });
    
    console.log('\n📈 Final Database Stats:');
    console.log(`   Total Suppliers: ${totalSuppliers}`);
    console.log(`   Active Suppliers: ${activeSuppliers}`);
    console.log(`   Verified Suppliers: ${verifiedSuppliers}`);

    console.log('\n🎉 Migration completed successfully!');
    console.log('🌐 You can now access the admin dashboard at: http://localhost:3000/dashboard');

  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\n📡 Disconnected from MongoDB');
  }
}

async function createSampleSuppliers() {
  console.log('📝 Creating sample suppliers...\n');
  
  const sampleSuppliers = [
    {
      name: 'SmokeDrop',
      priority: 'HIGH',
      verified: true,
      contact: {
        email: '<EMAIL>',
        phone: '******-0123',
        website: 'https://smokedrop.com'
      },
      automation: {
        requiresApproval: false,
        autoRefund: true,
        processingTime: '1-2 business days'
      },
      tags: ['vape', 'smoking', 'accessories'],
      shopifyVendorNames: ['SmokeDrop', 'Smoke Drop'],
      notes: 'High-volume supplier with automated processing'
    },
    {
      name: 'Buddify',
      priority: 'HIGH',
      verified: true,
      contact: {
        email: '<EMAIL>',
        phone: '******-0124',
        website: 'https://buddify.com'
      },
      automation: {
        requiresApproval: false,
        autoRefund: true,
        processingTime: '1-2 business days'
      },
      tags: ['cannabis', 'accessories', 'premium'],
      shopifyVendorNames: ['Buddify'],
      notes: 'Premium cannabis accessories supplier'
    },
    {
      name: 'GRAV®',
      priority: 'MEDIUM',
      verified: true,
      contact: {
        email: '<EMAIL>',
        phone: '******-0125',
        website: 'https://grav.com'
      },
      automation: {
        requiresApproval: true,
        autoRefund: false,
        processingTime: '3-5 business days'
      },
      tags: ['glass', 'premium', 'smoking'],
      shopifyVendorNames: ['GRAV®', 'GRAV', 'Grav Labs'],
      notes: 'Premium glass manufacturer requiring manual approval'
    },
    {
      name: 'Canna River',
      priority: 'MEDIUM',
      verified: true,
      contact: {
        email: '<EMAIL>',
        website: 'https://cannariver.com'
      },
      automation: {
        requiresApproval: false,
        autoRefund: false,
        processingTime: '2-3 business days'
      },
      tags: ['cbd', 'wellness', 'organic'],
      shopifyVendorNames: ['Canna River', 'CannaRiver'],
      notes: 'CBD and wellness products supplier'
    }
  ];

  let created = 0;
  for (const supplierData of sampleSuppliers) {
    try {
      const supplier = new Supplier(supplierData);
      await supplier.save();
      console.log(`   ✅ Created: ${supplierData.name}`);
      created++;
    } catch (error) {
      console.log(`   ❌ Failed to create: ${supplierData.name} - ${error.message}`);
    }
  }

  console.log(`\n📊 Created ${created} sample suppliers`);
}

async function addAdditionalSuppliers() {
  console.log('\n📝 Adding additional suppliers...');
  
  const additionalSuppliers = [
    {
      name: 'Vessel',
      priority: 'LOW',
      verified: false,
      contact: {
        email: '<EMAIL>',
        website: 'https://vessel.com'
      },
      tags: ['vape', 'premium', 'design'],
      shopifyVendorNames: ['Vessel']
    },
    {
      name: 'Discreet Smoker',
      priority: 'LOW',
      verified: false,
      contact: {
        email: '<EMAIL>'
      },
      tags: ['smoking', 'accessories', 'discreet'],
      shopifyVendorNames: ['Discreet Smoker', 'DiscreetSmoker']
    }
  ];

  for (const supplierData of additionalSuppliers) {
    try {
      // Check if supplier already exists
      const existing = await Supplier.findOne({ name: supplierData.name });
      if (!existing) {
        const supplier = new Supplier(supplierData);
        await supplier.save();
        console.log(`   ✅ Added: ${supplierData.name}`);
      } else {
        console.log(`   ⏭️  Skipped (exists): ${supplierData.name}`);
      }
    } catch (error) {
      console.log(`   ❌ Failed to add: ${supplierData.name} - ${error.message}`);
    }
  }
}

// Run migration
if (require.main === module) {
  migrateVendorsToMongoDB();
}

module.exports = { migrateVendorsToMongoDB };
