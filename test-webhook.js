const axios = require('axios');

// Test webhook payload based on the real webhook we received
const testPayload = {
  "request": {
    "approved": {"comment": null, "created_at": null, "status": false},
    "archived": {"comment": null, "created_at": null, "status": false},
    "channel": 97426,
    "created_at": "2025-07-18T12:41:08.238Z",
    "customer": {
      "address": {
        "address_line_1": "3183 WILSHIRE BLVD",
        "address_line_2": "213",
        "address_line_3": "",
        "city": "Los Angeles",
        "country": "United States",
        "country_code": "US",
        "first_name": "NATHAN KIM",
        "last_name": null,
        "postal_code": "90010",
        "province": "California",
        "province_code": "CA"
      },
      "bank": {
        "account_holder_name": "",
        "account_number": "",
        "confirm_account_number": "",
        "ifsc_code": ""
      },
      "email": "<EMAIL>",
      "id": *************,
      "name": "<PERSON>",
      "phone": ""
    },
    "id": "687a40e48140712e85f11f31",
    "incentive": null,
    "inspected": {"comment": null, "created_at": null, "status": false},
    "line_items": [{
      "exchange": {"order": null},
      "exchange_fee": {
        "price_set": {
          "presentment_money": {"amount": null, "currency_code": null},
          "shop_money": {"amount": null, "currency_code": null}
        }
      },
      "id": **************,
      "notes": null,
      "original_product": {
        "image": {"src": "https://cdn.shopify.com/s/files/1/0919/9881/4488/files/image_170678e4-ccdb-4d61-bdda-c3e0c0cc6555.jpg?v=**********"},
        "price": 64.99,
        "product_deleted": false,
        "product_id": *************,
        "sku": "BDDF_SKU5IwW27VcnvavFfOt",
        "title": "Grav Labs 6 inch Upright Bubbler",
        "variant_deleted": false,
        "variant_id": 49836981453080,
        "variant_title": null
      },
      "presentment_price": {
        "actual_amount": 64.99,
        "currency": "USD",
        "do_not_carry_forward_discount": true,
        "return_quantity": 1,
        "shipping_amount": 10.95,
        "taxes_included": false,
        "total_discount": 0,
        "total_tax": 0
      },
      "quantity": 1,
      "reason": "Others",
      "refund": {
        "actual_mode": null,
        "comment": null,
        "meta": null,
        "refunded_amount": {
          "presentment_money": {"currency_code": "USD"},
          "shop_money": {"currency_code": "USD"}
        },
        "refunded_at": null,
        "requested_mode": "store_credit",
        "status": "pending"
      },
      "return_fee": {
        "price_set": {
          "presentment_money": {"amount": 0, "currency_code": "USD"},
          "shop_money": {"amount": 0, "currency_code": "USD"}
        },
        "rule": null
      },
      "return_location": null,
      "shipping": [{"labels": [], "tracking_available": false}],
      "shop_price": {
        "actual_amount": 64.99,
        "currency": "USD",
        "do_not_carry_forward_discount": true,
        "return_quantity": 1,
        "shipping_amount": 10.95,
        "taxes_included": false,
        "total_discount": 0,
        "total_tax": 0
      },
      "shopify_order_fulfillment_location": null,
      "tags": []
    }],
    "manual_request": true,
    "order": {
      "created_at": "2025-07-17T19:19:08.000Z",
      "fulfillments": [],
      "id": 6518552461592,
      "name": "#1004",
      "order_manual_payment": false,
      "payment_gateways": []
    },
    "payment_details": null,
    "received": {"comment": null, "created_at": null, "status": false},
    "rejected": {"comment": null, "created_at": null, "status": false},
    "request_number": "RET3",
    "request_type": "return",
    "smart_exchange": false,
    "status": "requested",
    "unarchived": {"comment": null, "created_at": null, "status": false}
  }
};

async function testWebhook() {
  try {
    console.log('🚀 Testing Return Prime webhook...');
    
    const response = await axios.post('http://localhost:3000/webhook/return-prime', testPayload, {
      headers: {
        'Content-Type': 'application/json',
        'x-rp-hmac-sha512': 'test_signature',
        'x-rp-store': 'tgm1vh-mn.myshopify.com',
        'x-rp-topic': 'request/created'
      }
    });
    
    console.log('✅ Webhook test successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Webhook test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

testWebhook();
