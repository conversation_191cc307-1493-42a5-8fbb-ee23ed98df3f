# Render Logs Dashboard

A React-based dashboard for monitoring your Render services' logs in real-time without needing to access the Render dashboard directly.

## Features

- **Real-time Log Monitoring**: Stream logs from your Render services in real-time
- **Multi-service Support**: Monitor multiple services simultaneously
- **Advanced Filtering**: Filter logs by level (error, warning, info, debug) and search by content
- **Export Functionality**: Export filtered logs to JSON format
- **Auto-refresh**: Configurable auto-refresh intervals
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: Comprehensive error handling with user-friendly notifications
- **Secure Configuration**: API keys are stored locally and never transmitted unnecessarily

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- A Render account with API access

## Getting Started

### 1. Installation

```bash
# Clone or download this repository
cd render-logs-dashboard

# Install dependencies
npm install
```

### 2. Get Your Render API Key

1. Go to your [Render Account Settings](https://dashboard.render.com/u/settings)
2. Navigate to the "API Keys" section
3. Click "Create API Key"
4. Copy the generated API key (you'll only see it once!)

### 3. Configure Environment Variables (Optional)

You can set your API key and default settings using environment variables:

```bash
# Copy the example file
cp .env.example .env

# Edit .env and add your API key
VITE_RENDER_API_KEY=your_render_api_key_here
VITE_DEFAULT_REFRESH_INTERVAL=5000
VITE_DEFAULT_AUTO_REFRESH=true
```

**Benefits of using environment variables:**
- No need to enter API key in the UI every time
- Secure configuration for team environments
- Easy deployment configuration

### 4. Run the Application

```bash
# Start the development server
npm run dev
```

The application will be available at `http://localhost:5173`

### 5. Configure the Dashboard

**If you set up environment variables:**
1. Click the "Settings" button in the top-right corner
2. The API key will be automatically loaded from your `.env` file
3. Click "Test" to verify the connection
4. Select the services you want to monitor
5. Configure auto-refresh settings if desired
6. Click "Save"

**If you didn't set up environment variables:**
1. Click the "Settings" button in the top-right corner
2. Enter your Render API key manually
3. Click "Test" to verify the connection
4. Select the services you want to monitor
5. Configure auto-refresh settings if desired
6. Click "Save"

## Usage

### Basic Log Viewing

- Once configured, logs will automatically load from your selected services
- Use the search bar to filter logs by content
- Use the level dropdown to filter by log level (error, warning, info, debug)

### Real-time Monitoring

- Click the "Live" button to start real-time log streaming
- The dashboard will automatically fetch new logs based on your refresh interval
- Click "Stop" to pause real-time monitoring

### Exporting Logs

- Click the "Export" button to download filtered logs as a JSON file
- The export includes all currently visible logs based on your filters

### Clearing Logs

- Click the "Clear" button to remove all logs from the current view
- This only clears the display; it doesn't affect your actual service logs

## Configuration Options

### Environment Variables

You can configure default settings using environment variables in your `.env` file:

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_RENDER_API_KEY` | Your Render API key | None (required) |
| `VITE_DEFAULT_REFRESH_INTERVAL` | Default refresh interval in milliseconds | 5000 |
| `VITE_DEFAULT_AUTO_REFRESH` | Default auto-refresh setting | true |

### Auto-refresh Settings

- **Enable/Disable**: Toggle automatic log refreshing
- **Interval**: Set refresh interval from 1-60 seconds (default: 5 seconds)

### Service Selection

- Select one or more services to monitor
- Use "Select All" / "Deselect All" for quick selection
- Changes take effect immediately

## API Key Security

- API keys are stored locally in your browser's localStorage
- Keys are never transmitted to any third-party services
- You can clear your API key by removing it in the settings

## Troubleshooting

### Connection Issues

1. **Invalid API Key**: Verify your API key is correct and hasn't expired
2. **Network Issues**: Check your internet connection
3. **Service Access**: Ensure your API key has access to the selected services

### Performance Issues

1. **Too Many Logs**: Increase the refresh interval or use more specific filters
2. **Memory Usage**: Clear logs periodically or refresh the page
3. **Network Bandwidth**: Reduce the number of monitored services

### Common Error Messages

- **"Configuration Required"**: You need to set up your API key in settings
- **"No Services Selected"**: Select at least one service to monitor in settings
- **"Connection failed"**: Check your API key and internet connection
- **"Failed to fetch logs"**: Verify service access and try refreshing

## Development

### Building for Production

```bash
npm run build
```

### Code Structure

```
src/
├── components/          # React components
│   ├── Configuration.tsx    # Settings modal
│   ├── LogsDashboard.tsx   # Main dashboard
│   ├── ErrorBoundary.tsx   # Error handling
│   └── Toast.tsx           # Notifications
├── contexts/           # React contexts
│   └── ConfigContext.tsx   # Configuration management
├── services/           # API services
│   └── renderApi.ts        # Render API client
└── App.tsx            # Main application
```

## Technical Details

### API Integration

This dashboard uses the [Render REST API](https://render.com/docs/api) to:
- Authenticate using your API key
- Fetch your services list
- Retrieve logs with filtering and pagination
- Stream real-time logs using polling

### Data Flow

1. **Configuration**: API key and settings stored in localStorage
2. **Service Discovery**: Fetch available services on configuration
3. **Log Fetching**: Query logs endpoint with filters and pagination
4. **Real-time Updates**: Poll for new logs at configurable intervals
5. **Local Processing**: Filter, search, and format logs in the browser

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the [Render API documentation](https://render.com/docs/api)
3. Open an issue in this repository
