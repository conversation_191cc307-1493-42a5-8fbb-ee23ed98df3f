<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Terminal - Stripe Payment</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .pos-terminal {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            overflow: hidden;
            position: relative;
        }

        .terminal-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .terminal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }

        .terminal-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
        }

        .terminal-header p {
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .payment-form {
            padding: 30px;
        }

        .amount-display {
            text-align: center;
            margin-bottom: 30px;
        }

        .amount {
            font-size: 48px;
            font-weight: 700;
            color: #4CAF50;
            margin-bottom: 5px;
        }

        .currency {
            font-size: 14px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .stripe-element {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .stripe-element:focus-within {
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .stripe-element.StripeElement--invalid {
            border-color: #dc3545;
        }

        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .pay-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .pay-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .pay-button .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        .pay-button.loading .spinner {
            display: inline-block;
        }

        .pay-button.loading .button-text {
            opacity: 0.7;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-message {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            text-align: center;
            display: none;
        }

        .status-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .payment-methods {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .payment-method-icon {
            width: 40px;
            height: 25px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.6;
        }

        .visa { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 25"><rect fill="%23005bac" width="40" height="25" rx="4"/><text x="20" y="16" text-anchor="middle" fill="white" font-family="Arial" font-size="8" font-weight="bold">VISA</text></svg>'); }
        .mastercard { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 25"><rect fill="%23eb001b" width="40" height="25" rx="4"/><circle cx="15" cy="12.5" r="6" fill="%23ff5f00"/><circle cx="25" cy="12.5" r="6" fill="%23f79e1b"/></svg>'); }
        .amex { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 25"><rect fill="%23006fcf" width="40" height="25" rx="4"/><text x="20" y="16" text-anchor="middle" fill="white" font-family="Arial" font-size="6" font-weight="bold">AMEX</text></svg>'); }

        @media (max-width: 480px) {
            .pos-terminal {
                margin: 10px;
            }

            .amount {
                font-size: 36px;
            }

            .payment-form {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="pos-terminal">
        <div class="terminal-header">
            <h1>💳 Stripe Payment</h1>
            <p>Secure payment processing</p>
        </div>

        <div class="payment-form">
            <div class="amount-display">
                <div class="amount" id="amount-display">$5.00</div>
                <div class="currency">USD</div>
            </div>

            <form id="payment-form">
                <div class="form-group">
                    <label class="form-label" for="card-element">Card Information</label>
                    <div id="card-element" class="stripe-element">
                        <!-- Stripe Elements will create form elements here -->
                    </div>
                    <div id="card-errors" class="error-message"></div>
                </div>

                <button type="submit" id="submit-button" class="pay-button">
                    <div class="spinner"></div>
                    <span class="button-text">Pay $5.00</span>
                </button>
            </form>

            <div id="status-message" class="status-message"></div>

            <div class="payment-methods">
                <div class="payment-method-icon visa"></div>
                <div class="payment-method-icon mastercard"></div>
                <div class="payment-method-icon amex"></div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Stripe
        const stripe = Stripe('pk_test_your_publishable_key_here'); // Replace with your publishable key
        const elements = stripe.elements();

        // Create card element
        const cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                },
                invalid: {
                    color: '#9e2146',
                },
            },
            hidePostalCode: true
        });

        // Mount card element
        cardElement.mount('#card-element');

        // Handle real-time validation errors from the card Element
        cardElement.on('change', ({error}) => {
            const displayError = document.getElementById('card-errors');
            if (error) {
                displayError.textContent = error.message;
                displayError.style.display = 'block';
            } else {
                displayError.style.display = 'none';
            }
        });

        // Payment form variables
        let currentPaymentIntent = null;
        let isProcessing = false;

        // Create payment intent function
        async function createPaymentIntent(amount = 500) { // amount in cents
            try {
                const response = await fetch('/api/create-payment-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: amount,
                        currency: 'usd',
                        payment_method_types: ['card_present'],
                        metadata: {
                            manual_processing: 'true',
                            source: 'stripe_terminal',
                            transaction_id: `TXN_${Date.now()}`
                        }
                    }),
                });

                const data = await response.json();

                if (data.success) {
                    currentPaymentIntent = data.data.payment_intent;
                    console.log('Payment Intent created:', currentPaymentIntent);
                    return currentPaymentIntent;
                } else {
                    throw new Error(data.error || 'Failed to create payment intent');
                }
            } catch (error) {
                console.error('Error creating payment intent:', error);
                showStatus('Error creating payment intent: ' + error.message, 'error');
                return null;
            }
        }

        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            if (isProcessing) return;

            setLoading(true);
            isProcessing = true;

            try {
                // Create payment intent if not exists
                if (!currentPaymentIntent) {
                    const paymentIntent = await createPaymentIntent();
                    if (!paymentIntent) {
                        setLoading(false);
                        isProcessing = false;
                        return;
                    }
                }

                // Confirm payment with card element
                const {error, paymentIntent} = await stripe.confirmCardPayment(
                    currentPaymentIntent.client_secret,
                    {
                        payment_method: {
                            card: cardElement,
                        }
                    }
                );

                if (error) {
                    console.error('Payment failed:', error);
                    showStatus('Payment failed: ' + error.message, 'error');
                } else {
                    console.log('Payment succeeded:', paymentIntent);
                    showStatus('Payment successful! Transaction ID: ' + paymentIntent.id, 'success');

                    // Reset form after successful payment
                    setTimeout(() => {
                        resetForm();
                    }, 3000);
                }
            } catch (error) {
                console.error('Unexpected error:', error);
                showStatus('An unexpected error occurred: ' + error.message, 'error');
            }

            setLoading(false);
            isProcessing = false;
        });

        // Utility functions
        function setLoading(loading) {
            submitButton.disabled = loading;
            if (loading) {
                submitButton.classList.add('loading');
            } else {
                submitButton.classList.remove('loading');
            }
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('status-message');
            statusElement.textContent = message;
            statusElement.className = `status-message ${type}`;
            statusElement.style.display = 'block';

            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    statusElement.style.display = 'none';
                }, 5000);
            }
        }

        function resetForm() {
            cardElement.clear();
            currentPaymentIntent = null;
            document.getElementById('status-message').style.display = 'none';
            document.getElementById('card-errors').style.display = 'none';
        }

        // Initialize payment intent on page load
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('Initializing payment form...');

            // You can uncomment this to create payment intent immediately
            // await createPaymentIntent();

            console.log('Payment form ready');
        });

        // Handle payment intent from external source (like your API response)
        function handleExternalPaymentIntent(paymentIntentData) {
            if (paymentIntentData && paymentIntentData.client_secret) {
                currentPaymentIntent = paymentIntentData;
                console.log('External payment intent loaded:', currentPaymentIntent);

                // Update amount display
                const amount = (currentPaymentIntent.amount / 100).toFixed(2);
                document.getElementById('amount-display').textContent = `$${amount}`;
                document.querySelector('.pay-button .button-text').textContent = `Pay $${amount}`;

                showStatus('Payment intent loaded successfully', 'success');
            }
        }

        // Example: Handle the payment intent from your API response
        // You can call this function with your payment intent data
        const examplePaymentIntent = {
            "id": "pi_3RnhT6Roh11lQUTE0BDsVxMV",
            "object": "payment_intent",
            "amount": 500,
            "client_secret": "pi_3RnhT6Roh11lQUTE0BDsVxMV_secret_eTQXEdYQulOIKTqYFbOQGbf7h",
            "status": "requires_payment_method"
        };

        // Uncomment to test with your payment intent
        // handleExternalPaymentIntent(examplePaymentIntent);
    </script>
</body>
</html>