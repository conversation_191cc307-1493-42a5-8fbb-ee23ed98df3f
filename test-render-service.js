const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:3000';

async function testRenderService() {
  console.log('🧪 Testing Render Service Integration...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed');
    console.log('   Endpoints available:', Object.keys(healthResponse.data.endpoints));
    console.log('');

    // Test 2: Test connection (without API key)
    console.log('2. Testing connection without API key...');
    try {
      const connectionResponse = await axios.get(`${BASE_URL}/render/test-connection`);
      if (connectionResponse.data.success) {
        console.log('✅ Connection test passed (API key already configured)');
      } else {
        console.log('⚠️  Connection test failed (expected without API key)');
      }
    } catch (error) {
      console.log('⚠️  Connection test failed (expected without API key)');
    }
    console.log('');

    // Test 3: Try to get services (should fail without API key)
    console.log('3. Testing services endpoint without API key...');
    try {
      const servicesResponse = await axios.get(`${BASE_URL}/render/services`);
      if (servicesResponse.data.success) {
        console.log('✅ Services endpoint accessible');
        console.log(`   Found ${servicesResponse.data.count} services`);
      } else {
        console.log('⚠️  Services endpoint failed (expected without API key)');
      }
    } catch (error) {
      console.log('⚠️  Services endpoint failed (expected without API key)');
      console.log('   Error:', error.response?.data?.error || error.message);
    }
    console.log('');

    // Test 4: Try to get logs (should fail without API key)
    console.log('4. Testing logs endpoint without API key...');
    try {
      const logsResponse = await axios.get(`${BASE_URL}/render/logs`);
      if (logsResponse.data.success) {
        console.log('✅ Logs endpoint accessible');
        console.log(`   Found ${logsResponse.data.data.logs?.length || 0} log entries`);
      } else {
        console.log('⚠️  Logs endpoint failed (expected without API key)');
      }
    } catch (error) {
      console.log('⚠️  Logs endpoint failed (expected without API key)');
      console.log('   Error:', error.response?.data?.error || error.message);
    }
    console.log('');

    // Test 5: Test API key update with invalid key
    console.log('5. Testing API key update with invalid key...');
    try {
      const updateResponse = await axios.post(`${BASE_URL}/render/update-api-key`, {
        apiKey: 'invalid_test_key_123'
      });
      
      if (updateResponse.data.success) {
        console.log('⚠️  API key update unexpectedly succeeded with invalid key');
      } else {
        console.log('✅ API key update correctly rejected invalid key');
      }
    } catch (error) {
      console.log('✅ API key update correctly rejected invalid key');
      console.log('   Error:', error.response?.data?.error || error.message);
    }
    console.log('');

    // Test 6: Check dashboard accessibility
    console.log('6. Testing dashboard accessibility...');
    try {
      const dashboardResponse = await axios.get(`${BASE_URL}/render-dashboard`);
      if (dashboardResponse.status === 200) {
        console.log('✅ Render dashboard is accessible');
        console.log('   Content-Type:', dashboardResponse.headers['content-type']);
      }
    } catch (error) {
      console.log('❌ Dashboard not accessible');
      console.log('   Error:', error.message);
    }
    console.log('');

    console.log('🎉 Render Service Integration Test Complete!\n');
    console.log('📋 Summary:');
    console.log('   - Service endpoints are properly configured');
    console.log('   - Error handling works correctly without API key');
    console.log('   - Dashboard is accessible');
    console.log('   - Ready for API key configuration');
    console.log('');
    console.log('🔧 Next Steps:');
    console.log('   1. Get your Render API key from https://dashboard.render.com/');
    console.log('   2. Add RENDER_API_KEY to your .env file');
    console.log('   3. Restart the server');
    console.log('   4. Visit http://localhost:3000/render-dashboard to use the dashboard');
    console.log('');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('💡 Make sure your server is running:');
      console.log('   npm run dev');
    }
  }
}

// Run the test
testRenderService();
