/**
 * Test script for Google Sheets integration
 * Run with: node test-google-sheets.js
 */

const { google } = require('googleapis');
require('dotenv').config();

async function testGoogleSheetsIntegration() {
  console.log('🧪 Testing Google Sheets Integration...\n');

  // Check environment variables
  console.log('1. Checking environment variables...');
  const requiredVars = [
    'GOOGLE_SHEETS_ID',
    'GOOGLE_SERVICE_ACCOUNT_EMAIL',
    'GOOGLE_PRIVATE_KEY'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(varName => console.error(`   - ${varName}`));
    console.error('\nPlease check your .env file and refer to GOOGLE_SHEETS_SETUP.md');
    process.exit(1);
  }
  
  console.log('✅ All required environment variables are set\n');

  try {
    // Initialize Google Sheets API
    console.log('2. Initializing Google Sheets API...');
    
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n')
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets']
    });

    const sheets = google.sheets({ version: 'v4', auth });
    console.log('✅ Google Sheets API initialized\n');

    // Test spreadsheet access
    console.log('3. Testing spreadsheet access...');
    const spreadsheetId = process.env.GOOGLE_SHEETS_ID;
    
    const spreadsheetInfo = await sheets.spreadsheets.get({
      spreadsheetId: spreadsheetId
    });
    
    console.log(`✅ Successfully accessed spreadsheet: "${spreadsheetInfo.data.properties.title}"`);
    console.log(`   URL: https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit\n`);

    // Check if Returns sheet exists
    console.log('4. Checking for Returns sheet...');
    const returnsSheet = spreadsheetInfo.data.sheets.find(
      sheet => sheet.properties.title === 'Returns'
    );

    if (!returnsSheet) {
      console.log('⚠️  Returns sheet not found, creating it...');
      
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId: spreadsheetId,
        resource: {
          requests: [{
            addSheet: {
              properties: {
                title: 'Returns'
              }
            }
          }]
        }
      });
      
      console.log('✅ Returns sheet created\n');
    } else {
      console.log('✅ Returns sheet found\n');
    }

    // Test writing headers
    console.log('5. Testing header creation...');
    
    const headers = [
      'Timestamp',
      'Return ID',
      'Order ID',
      'SKU',
      'Supplier',
      'Quantity',
      'Status',
      'Customer Email',
      'Reason',
      'Event Type',
      'Processed At',
      'Error',
      'Date',
      'Time',
      'Result',
      'Supplier Status'
    ];

    await sheets.spreadsheets.values.update({
      spreadsheetId: spreadsheetId,
      range: 'Returns!A1:P1',
      valueInputOption: 'USER_ENTERED',
      resource: {
        values: [headers]
      }
    });

    // Format headers
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId: spreadsheetId,
      resource: {
        requests: [
          {
            repeatCell: {
              range: {
                sheetId: returnsSheet ? returnsSheet.properties.sheetId : 0,
                startRowIndex: 0,
                endRowIndex: 1,
                startColumnIndex: 0,
                endColumnIndex: 16
              },
              cell: {
                userEnteredFormat: {
                  backgroundColor: { red: 0.2, green: 0.6, blue: 1.0 },
                  textFormat: { bold: true, foregroundColor: { red: 1.0, green: 1.0, blue: 1.0 } }
                }
              },
              fields: 'userEnteredFormat(backgroundColor,textFormat)'
            }
          }
        ]
      }
    });

    console.log('✅ Headers created and formatted\n');

    // Test writing sample data
    console.log('6. Testing sample data write...');
    
    const now = new Date();
    const sampleData = [
      now.toISOString(),
      'TEST-' + Date.now(),
      'ORDER-12345',
      'TEST-SKU-001',
      'Test Supplier',
      '1',
      'processed',
      '<EMAIL>',
      'Testing integration',
      'test',
      now.toISOString(),
      '',
      now.toLocaleDateString(),
      now.toLocaleTimeString(),
      'SUCCESS',
      'IDENTIFIED'
    ];

    await sheets.spreadsheets.values.append({
      spreadsheetId: spreadsheetId,
      range: 'Returns!A:P',
      valueInputOption: 'USER_ENTERED',
      insertDataOption: 'INSERT_ROWS',
      resource: {
        values: [sampleData]
      }
    });

    console.log('✅ Sample data written successfully\n');

    // Test reading data back
    console.log('7. Testing data read...');
    
    const readResponse = await sheets.spreadsheets.values.get({
      spreadsheetId: spreadsheetId,
      range: 'Returns!A1:P10'
    });

    const rows = readResponse.data.values || [];
    console.log(`✅ Successfully read ${rows.length} rows from the sheet\n`);

    // Summary
    console.log('🎉 Google Sheets Integration Test Complete!');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('✅ All tests passed successfully');
    console.log(`📊 Spreadsheet: ${spreadsheetInfo.data.properties.title}`);
    console.log(`🔗 URL: https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit`);
    console.log(`📝 Data rows: ${rows.length - 1} (excluding header)`);
    console.log('\nYour Google Sheets integration is ready to use!');
    console.log('You can now start your server and process returns.');

  } catch (error) {
    console.error('\n❌ Google Sheets Integration Test Failed!');
    console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.error('Error details:', error.message);
    
    if (error.code === 403) {
      console.error('\n🔧 Possible solutions:');
      console.error('1. Check that the service account has access to the spreadsheet');
      console.error('2. Verify the service account email is correct');
      console.error('3. Ensure the spreadsheet is shared with the service account');
    } else if (error.code === 404) {
      console.error('\n🔧 Possible solutions:');
      console.error('1. Check that the GOOGLE_SHEETS_ID is correct');
      console.error('2. Verify the spreadsheet exists and is accessible');
    } else if (error.message.includes('private_key')) {
      console.error('\n🔧 Possible solutions:');
      console.error('1. Check the GOOGLE_PRIVATE_KEY format in your .env file');
      console.error('2. Ensure newlines are properly escaped');
    }
    
    console.error('\nRefer to GOOGLE_SHEETS_SETUP.md for detailed setup instructions.');
    process.exit(1);
  }
}

// Run the test
testGoogleSheetsIntegration();
