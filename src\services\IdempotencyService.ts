import logger from '../utils/logger';

interface IdempotencyEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

interface RetryEntry {
  attempt: number;
  error: string;
  timestamp: number;
  ttl: number;
}

export class IdempotencyService {
  private processedWebhooks: Map<string, IdempotencyEntry> = new Map();
  private retryAttempts: Map<string, RetryEntry> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);

    logger.info('In-memory idempotency service initialized');
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();

    // Clean up processed webhooks
    for (const [key, entry] of this.processedWebhooks.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.processedWebhooks.delete(key);
      }
    }

    // Clean up retry attempts
    for (const [key, entry] of this.retryAttempts.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.retryAttempts.delete(key);
      }
    }

    logger.debug('Cleaned up expired idempotency entries', {
      processedWebhooks: this.processedWebhooks.size,
      retryAttempts: this.retryAttempts.size
    });
  }

  /**
   * Check if a webhook has already been processed
   */
  public async checkProcessed(idempotencyKey: string): Promise<boolean> {
    const entry = this.processedWebhooks.get(idempotencyKey);
    if (!entry) {
      return false;
    }

    // Check if entry has expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.processedWebhooks.delete(idempotencyKey);
      return false;
    }

    return true;
  }

  /**
   * Mark a webhook as processed
   */
  public async markProcessed(idempotencyKey: string, data?: any): Promise<boolean> {
    try {
      const entry: IdempotencyEntry = {
        data: data || 'processed',
        timestamp: Date.now(),
        ttl: 24 * 60 * 60 * 1000 // 24 hours in milliseconds
      };

      this.processedWebhooks.set(idempotencyKey, entry);
      return true;
    } catch (error) {
      logger.error('Error marking webhook as processed', { error, idempotencyKey });
      return false;
    }
  }

  /**
   * Get processing result for an idempotency key
   */
  public async getProcessingResult(idempotencyKey: string): Promise<any | null> {
    const entry = this.processedWebhooks.get(idempotencyKey);
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.processedWebhooks.delete(idempotencyKey);
      return null;
    }

    return entry.data;
  }

  /**
   * Store retry attempt information
   */
  public async storeRetryAttempt(
    idempotencyKey: string,
    attempt: number,
    error: string
  ): Promise<boolean> {
    try {
      const retryEntry: RetryEntry = {
        attempt,
        error,
        timestamp: Date.now(),
        ttl: 60 * 60 * 1000 // 1 hour in milliseconds
      };

      this.retryAttempts.set(idempotencyKey, retryEntry);
      return true;
    } catch (error) {
      logger.error('Error storing retry attempt', { error, idempotencyKey });
      return false;
    }
  }

  /**
   * Get retry attempt count
   */
  public async getRetryAttempt(idempotencyKey: string): Promise<number> {
    const entry = this.retryAttempts.get(idempotencyKey);
    if (!entry) {
      return 0;
    }

    // Check if entry has expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.retryAttempts.delete(idempotencyKey);
      return 0;
    }

    return entry.attempt;
  }

  /**
   * Clear retry attempts for a key
   */
  public async clearRetryAttempts(idempotencyKey: string): Promise<boolean> {
    try {
      this.retryAttempts.delete(idempotencyKey);
      return true;
    } catch (error) {
      logger.error('Error clearing retry attempts', { error, idempotencyKey });
      return false;
    }
  }

  /**
   * Test in-memory storage
   */
  public async testConnection(): Promise<boolean> {
    try {
      // Test by storing and retrieving a value
      const testKey = 'test:connection';
      await this.markProcessed(testKey, 'test');
      const result = await this.getProcessingResult(testKey);
      this.processedWebhooks.delete(testKey);

      return result === 'test';
    } catch (error) {
      logger.error('In-memory storage test failed', { error });
      return false;
    }
  }

  /**
   * Close and cleanup
   */
  public async close(): Promise<void> {
    try {
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }
      this.processedWebhooks.clear();
      this.retryAttempts.clear();
      logger.info('In-memory idempotency service closed');
    } catch (error) {
      logger.error('Error closing idempotency service', { error });
    }
  }
}
