import React, { useState, useEffect, useRef } from 'react';
import { Search, Filter, Download, Play, Pause, RefreshCw, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { useConfig } from '../contexts/ConfigContext';
import { useToast } from './Toast';
import type { LogEntry } from '../services/renderApi';

const LogsDashboard: React.FC = () => {
  const {
    renderApi,
    isConfigured,
    selectedServices,
    autoRefresh,
    refreshInterval,
  } = useConfig();

  const { addToast } = useToast();

  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState<string>('');
  const [isRealTimeActive, setIsRealTimeActive] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState<Date | null>(null);

  const unsubscribeRef = useRef<(() => void) | null>(null);
  const logsEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when new logs arrive
  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Filter logs based on search term and level
  useEffect(() => {
    let filtered = logs;

    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.source.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (levelFilter) {
      filtered = filtered.filter(log => log.level === levelFilter);
    }

    setFilteredLogs(filtered);
  }, [logs, searchTerm, levelFilter]);

  // Fetch initial logs
  const fetchLogs = async (append = false) => {
    if (!renderApi || selectedServices.length === 0) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await renderApi.getLogs({
        resourceIds: selectedServices,
        limit: 100,
        direction: 'backward',
      });

      if (append) {
        setLogs(prev => [...prev, ...response.logs]);
      } else {
        setLogs(response.logs);
      }

      setLastFetchTime(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch logs';
      setError(errorMessage);
      addToast({
        type: 'error',
        title: 'Failed to fetch logs',
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Start real-time log monitoring
  const startRealTime = async () => {
    if (!renderApi || selectedServices.length === 0) return;

    setIsRealTimeActive(true);
    setError(null);

    try {
      const unsubscribe = await renderApi.subscribeToLogs(
        {
          resourceIds: selectedServices,
          level: levelFilter || undefined,
          search: searchTerm || undefined,
        },
        (newLogs) => {
          if (newLogs.length > 0) {
            setLogs(prev => [...prev, ...newLogs]);
            setTimeout(scrollToBottom, 100);
          }
        },
        refreshInterval
      );

      unsubscribeRef.current = unsubscribe;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start real-time monitoring';
      setError(errorMessage);
      addToast({
        type: 'error',
        title: 'Real-time monitoring failed',
        message: errorMessage,
      });
      setIsRealTimeActive(false);
    }
  };

  // Stop real-time monitoring
  const stopRealTime = () => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }
    setIsRealTimeActive(false);
  };

  // Toggle real-time monitoring
  const toggleRealTime = () => {
    if (isRealTimeActive) {
      stopRealTime();
    } else {
      startRealTime();
    }
  };

  // Clear logs
  const clearLogs = () => {
    setLogs([]);
    setError(null);
    addToast({
      type: 'info',
      title: 'Logs cleared',
      message: 'All logs have been cleared from the display.',
    });
  };

  // Export logs to JSON
  const exportLogs = () => {
    const dataStr = JSON.stringify(filteredLogs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `render-logs-${format(new Date(), 'yyyy-MM-dd-HH-mm-ss')}.json`;
    link.click();
    URL.revokeObjectURL(url);

    addToast({
      type: 'success',
      title: 'Logs exported',
      message: `${filteredLogs.length} logs exported successfully.`,
    });
  };

  // Get log level color
  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'error':
        return 'text-red-600 bg-red-50';
      case 'warn':
      case 'warning':
        return 'text-yellow-600 bg-yellow-50';
      case 'info':
        return 'text-blue-600 bg-blue-50';
      case 'debug':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // Auto-start real-time if enabled
  useEffect(() => {
    if (autoRefresh && isConfigured && selectedServices.length > 0) {
      startRealTime();
    }

    return () => {
      stopRealTime();
    };
  }, [autoRefresh, isConfigured, selectedServices, refreshInterval]);

  // Initial fetch when configuration changes
  useEffect(() => {
    if (isConfigured && selectedServices.length > 0) {
      fetchLogs();
    }
  }, [isConfigured, selectedServices]);

  if (!isConfigured) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Configuration Required</h3>
          <p className="text-gray-600">Please configure your Render API key to start monitoring logs.</p>
        </div>
      </div>
    );
  }

  if (selectedServices.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Services Selected</h3>
          <p className="text-gray-600">Please select at least one service to monitor in the configuration.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Controls */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search */}
          <div className="relative flex-1 min-w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Level Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={levelFilter}
              onChange={(e) => setLevelFilter(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Levels</option>
              <option value="error">Error</option>
              <option value="warn">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => fetchLogs()}
              disabled={isLoading}
              className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>

            <button
              onClick={toggleRealTime}
              className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                isRealTimeActive
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              {isRealTimeActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {isRealTimeActive ? 'Stop' : 'Live'}
            </button>

            <button
              onClick={exportLogs}
              disabled={filteredLogs.length === 0}
              className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Export
            </button>

            <button
              onClick={clearLogs}
              className="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Clear
            </button>
          </div>
        </div>

        {/* Status */}
        <div className="mt-3 flex items-center justify-between text-sm text-gray-600">
          <div>
            Showing {filteredLogs.length} of {logs.length} logs
            {selectedServices.length > 0 && (
              <span className="ml-2">
                from {selectedServices.length} service{selectedServices.length !== 1 ? 's' : ''}
              </span>
            )}
          </div>
          <div className="flex items-center gap-4">
            {isRealTimeActive && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Live</span>
              </div>
            )}
            {lastFetchTime && (
              <span>Last updated: {format(lastFetchTime, 'HH:mm:ss')}</span>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Logs Display */}
      <div className="flex-1 overflow-auto bg-gray-50">
        {isLoading && logs.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="w-8 h-8 animate-spin text-gray-400" />
          </div>
        ) : filteredLogs.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-gray-500">No logs found</p>
              {searchTerm || levelFilter ? (
                <p className="text-sm text-gray-400 mt-1">Try adjusting your filters</p>
              ) : null}
            </div>
          </div>
        ) : (
          <div className="p-4 space-y-2">
            {filteredLogs.map((log, index) => (
              <div
                key={`${log.id}-${index}`}
                className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(log.level)}`}
                    >
                      {log.level.toUpperCase()}
                    </span>
                    <span className="text-sm text-gray-500">{log.source}</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {format(new Date(log.timestamp), 'MMM dd, HH:mm:ss.SSS')}
                  </span>
                </div>
                <div className="text-sm font-mono text-gray-900 whitespace-pre-wrap break-words">
                  {log.message}
                </div>
              </div>
            ))}
            <div ref={logsEndRef} />
          </div>
        )}
      </div>
    </div>
  );
};

export default LogsDashboard;
