import { Request, Response, NextFunction } from 'express';
import { verifyWebhookSignature } from '../utils/security';
import { validateWebhookPayload } from '../utils/validation';
import logger from '../utils/logger';

// Extend Request interface to include rawBody
declare global {
  namespace Express {
    interface Request {
      rawBody?: Buffer;
    }
  }
}

/**
 * Middleware to capture raw body for signature verification
 */
export function captureRawBody(req: Request, _res: Response, next: NextFunction): void {
  let data = '';
  req.setEncoding('utf8');
  
  req.on('data', (chunk) => {
    data += chunk;
  });
  
  req.on('end', () => {
    req.rawBody = Buffer.from(data, 'utf8');
    req.body = data;
    next();
  });
}

/**
 * Middleware to verify webhook signature
 */
export function verifySignature(req: Request, res: Response, next: NextFunction): void {
  try {
    // Return Prime uses x-rp-hmac-sha512 header
    const signature = req.headers['x-rp-hmac-sha512'] || req.headers['x-signature'] || req.headers['x-hub-signature-256'];

    if (!signature || typeof signature !== 'string') {
      logger.warn('Missing webhook signature', {
        headers: req.headers,
        ip: req.ip
      });
      res.status(401).json({ error: 'Missing signature' });
      return;
    }
    
    if (!req.rawBody) {
      logger.error('Missing raw body for signature verification');
      res.status(400).json({ error: 'Invalid request body' });
      return;
    }
    
    const isValid = verifyWebhookSignature(req.rawBody.toString(), signature);
    
    if (!isValid) {
      logger.warn('Invalid webhook signature', { 
        signature,
        ip: req.ip 
      });
      res.status(401).json({ error: 'Invalid signature' });
      return;
    }
    
    logger.debug('Webhook signature verified successfully');
    next();
  } catch (error) {
    logger.error('Error verifying webhook signature', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Middleware to validate webhook payload
 */
export function validatePayload(req: Request, res: Response, next: NextFunction): void {
  try {
    let payload;

    // Parse JSON if body is string
    if (typeof req.body === 'string') {
      try {
        payload = JSON.parse(req.body);
      } catch (parseError) {
        logger.warn('Invalid JSON in webhook payload', { body: req.body });
        res.status(400).json({ error: 'Invalid JSON payload' });
        return;
      }
    } else {
      payload = req.body;
    }

    // Log the raw payload for debugging
    logger.info('Raw webhook payload received', {
      payload,
      headers: {
        'x-rp-hmac-sha512': req.headers['x-rp-hmac-sha512'],
        'x-rp-store': req.headers['x-rp-store'],
        'x-rp-topic': req.headers['x-rp-topic']
      }
    });

    const { error, value } = validateWebhookPayload(payload);

    if (error) {
      logger.warn('Webhook payload validation warnings (continuing anyway)', {
        warnings: error,
        requestId: payload?.request?.id,
        status: payload?.request?.status
      });
      // Always continue processing - use original payload
      req.body = payload;
    } else {
      // Use validated payload if validation passed
      req.body = value;
      logger.debug('Webhook payload validated successfully', {
        requestId: value?.request?.id,
        status: value?.request?.status
      });
    }

    next();
  } catch (error) {
    logger.error('Error validating webhook payload', { error });
    res.status(500).json({ error: 'Internal server error' });
  }
}
