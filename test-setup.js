// Simple test to check if the setup works
console.log('🧪 Testing basic setup...\n');

try {
  // Test environment variables
  require('dotenv').config();
  console.log('✅ dotenv loaded');
  
  // Test basic imports
  const express = require('express');
  console.log('✅ express imported');
  
  const mongoose = require('mongoose');
  console.log('✅ mongoose imported');
  
  // Test MongoDB connection
  console.log('\n🔌 Testing MongoDB connection...');
  
  const MONGODB_URI = process.env.MONGODB_URI;
  const DB_NAME = process.env.MONGODB_DB_NAME;
  
  if (!MONGODB_URI) {
    console.log('❌ MONGODB_URI not found in environment');
    process.exit(1);
  }
  
  console.log(`📋 URI: ${MONGODB_URI.replace(/\/\/.*@/, '//***:***@')}`);
  console.log(`📋 Database: ${DB_NAME}`);
  
  // Test connection
  mongoose.connect(MONGODB_URI, {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 10000,
    socketTimeoutMS: 45000
  }).then(() => {
    console.log('✅ MongoDB connected successfully!');
    console.log(`   Ready State: ${mongoose.connection.readyState}`);
    console.log(`   Host: ${mongoose.connection.host}`);
    console.log(`   Database: ${mongoose.connection.name}`);
    
    console.log('\n🎉 All tests passed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Run: node migrate-vendors-to-mongodb.js');
    console.log('   2. Run: npm run build');
    console.log('   3. Run: npm start');
    
    mongoose.disconnect();
  }).catch(error => {
    console.error('❌ MongoDB connection failed:', error.message);
    
    if (error.message.includes('Authentication failed') || error.message.includes('bad auth')) {
      console.log('\n💡 Authentication Error - Check your MongoDB credentials');
    } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Connection Error - Check if MongoDB is running');
    }
    
    process.exit(1);
  });
  
} catch (error) {
  console.error('❌ Setup test failed:', error.message);
  process.exit(1);
}
