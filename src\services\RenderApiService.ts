import axios, { type AxiosResponse } from 'axios';
import logger from '../utils/logger';

export interface LogEntry {
  id: string;
  timestamp: string;
  message: string;
  level: string;
  source: string;
  resourceId: string;
  resourceType: string;
}

export interface LogsResponse {
  logs: LogEntry[];
  hasMore: boolean;
  nextStartTime?: string;
  nextEndTime?: string;
}

export interface Service {
  id: string;
  name: string;
  type: string;
  status: string;
  region: string;
  createdAt: string;
  updatedAt: string;
}

export interface ServicesResponse {
  services: Service[];
}

export interface RenderApiConfig {
  apiKey: string;
  baseUrl?: string;
}

class RenderApiService {
  private apiKey: string;
  private baseUrl: string;
  private axiosInstance;

  constructor(config: RenderApiConfig) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || 'https://api.render.com/v1';
    
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });
  }

  // Update API key
  updateApiKey(apiKey: string): void {
    this.apiKey = apiKey;
    this.axiosInstance.defaults.headers['Authorization'] = `Bearer ${apiKey}`;
  }

  // Get all services
  async getServices(): Promise<Service[]> {
    try {
      const response: AxiosResponse<ServicesResponse> = await this.axiosInstance.get('/services');
      logger.info('Successfully fetched Render services', { count: response.data.services?.length || 0 });
      return response.data.services || [];
    } catch (error: any) {
      logger.error('Error fetching Render services', { error: error.message });
      throw new Error('Failed to fetch services. Please check your API key and try again.');
    }
  }

  // Get logs for specific resources
  async getLogs(params: {
    resourceIds?: string[];
    startTime?: string;
    endTime?: string;
    limit?: number;
    direction?: 'forward' | 'backward';
    level?: string;
    search?: string;
  }): Promise<LogsResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.resourceIds && params.resourceIds.length > 0) {
        params.resourceIds.forEach(id => queryParams.append('resourceId', id));
      }
      
      if (params.startTime) {
        queryParams.append('startTime', params.startTime);
      }
      
      if (params.endTime) {
        queryParams.append('endTime', params.endTime);
      }
      
      if (params.limit) {
        queryParams.append('limit', params.limit.toString());
      }
      
      if (params.direction) {
        queryParams.append('direction', params.direction);
      }
      
      if (params.level) {
        queryParams.append('level', params.level);
      }
      
      if (params.search) {
        queryParams.append('search', params.search);
      }

      const response: AxiosResponse<LogsResponse> = await this.axiosInstance.get(
        `/logs?${queryParams.toString()}`
      );
      
      logger.info('Successfully fetched Render logs', { 
        count: response.data.logs?.length || 0,
        hasMore: response.data.hasMore 
      });
      
      return response.data;
    } catch (error: any) {
      logger.error('Error fetching Render logs', { error: error.message });
      throw new Error('Failed to fetch logs. Please check your configuration and try again.');
    }
  }

  // Get logs with real-time updates (polling)
  async subscribeToLogs(
    params: {
      resourceIds?: string[];
      level?: string;
      search?: string;
    },
    callback: (logs: LogEntry[]) => void,
    intervalMs: number = 5000
  ): Promise<() => void> {
    let isActive = true;
    let lastTimestamp: string | undefined;

    logger.info('Starting Render logs subscription', { intervalMs, params });

    const poll = async () => {
      if (!isActive) return;

      try {
        const now = new Date().toISOString();
        const startTime = lastTimestamp || new Date(Date.now() - 60000).toISOString(); // Last minute if no previous timestamp

        const response = await this.getLogs({
          ...params,
          startTime,
          endTime: now,
          direction: 'forward',
          limit: 100,
        });

        if (response.logs && response.logs.length > 0) {
          callback(response.logs);
          lastTimestamp = response.logs[response.logs.length - 1].timestamp;
        }
      } catch (error: any) {
        logger.error('Error in Render log subscription', { error: error.message });
      }

      if (isActive) {
        setTimeout(poll, intervalMs);
      }
    };

    // Start polling
    poll();

    // Return unsubscribe function
    return () => {
      isActive = false;
      logger.info('Stopped Render logs subscription');
    };
  }

  // Test API connection
  async testConnection(): Promise<boolean> {
    try {
      await this.getServices();
      logger.info('Render API connection test successful');
      return true;
    } catch (error: any) {
      logger.error('Render API connection test failed', { error: error.message });
      return false;
    }
  }

  // Get service by ID
  async getService(serviceId: string): Promise<Service | null> {
    try {
      const response: AxiosResponse<Service> = await this.axiosInstance.get(`/services/${serviceId}`);
      logger.info('Successfully fetched Render service', { serviceId, name: response.data.name });
      return response.data;
    } catch (error: any) {
      logger.error('Error fetching Render service', { serviceId, error: error.message });
      return null;
    }
  }

  // Get service logs by service ID
  async getServiceLogs(serviceId: string, params?: {
    startTime?: string;
    endTime?: string;
    limit?: number;
    direction?: 'forward' | 'backward';
    level?: string;
    search?: string;
  }): Promise<LogsResponse> {
    return this.getLogs({
      resourceIds: [serviceId],
      ...params
    });
  }
}

export default RenderApiService;
