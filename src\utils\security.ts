import crypto from 'crypto';
import { config } from '../config';

/**
 * Verify webhook signature from Return Prime
 * Return Prime uses HMAC-SHA512 signature verification
 */
export function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string = config.webhookSecret
): boolean {
  try {
    // Return Prime sends SHA512 signatures without prefix
    const cleanSignature = signature.replace(/^sha512=/, '').replace(/^sha256=/, '');

    // Try SHA512 first (Return Prime format)
    try {
      const expectedSignature512 = crypto
        .createHmac('sha512', secret)
        .update(payload, 'utf8')
        .digest('hex');

      if (crypto.timingSafeEqual(
        Buffer.from(cleanSignature, 'hex'),
        Buffer.from(expectedSignature512, 'hex')
      )) {
        return true;
      }
    } catch (sha512Error) {
      // Continue to try SHA256
    }

    // Fallback to SHA256 for other webhook providers
    try {
      const expectedSignature256 = crypto
        .createHmac('sha256', secret)
        .update(payload, 'utf8')
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(cleanSignature, 'hex'),
        Buffer.from(expectedSignature256, 'hex')
      );
    } catch (sha256Error) {
      return false;
    }
  } catch (error) {
    return false;
  }
}

/**
 * Generate a unique idempotency key for a webhook payload
 */
export function generateIdempotencyKey(returnId: string, eventType: string, timestamp: string): string {
  return crypto
    .createHash('sha256')
    .update(`${returnId}-${eventType}-${timestamp}`)
    .digest('hex');
}

/**
 * Sanitize email content to prevent injection attacks
 */
export function sanitizeEmailContent(content: string): string {
  return content
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/data:/gi, '') // Remove data: protocols
    .trim();
}
