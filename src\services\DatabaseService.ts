import mongoose from 'mongoose';
import { config } from '../config';
import logger from '../utils/logger';

export class DatabaseService {
  private static instance: DatabaseService;
  private isConnected: boolean = false;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Connect to MongoDB
   */
  public async connect(): Promise<void> {
    try {
      if (this.isConnected) {
        logger.info('MongoDB already connected');
        return;
      }

      // Check if MongoDB config is available
      if (!config.mongodb) {
        throw new Error('MongoDB configuration not available - using vendor database JSON instead');
      }

      // Build MongoDB URI
      let mongoUri: string;
      if (config.mongodb.uri.includes('mongodb+srv://') || config.mongodb.uri.includes('@')) {
        // Atlas or authenticated URI - use as is and append database name if not included
        mongoUri = config.mongodb.uri.includes('?')
          ? config.mongodb.uri.replace('?', `/${config.mongodb.dbName}?`)
          : `${config.mongodb.uri}/${config.mongodb.dbName}`;
      } else {
        // Local MongoDB without auth
        mongoUri = `${config.mongodb.uri}/${config.mongodb.dbName}`;
      }

      const connectionOptions = {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 10000, // Increased timeout
        socketTimeoutMS: 45000,
        bufferCommands: false,
        bufferMaxEntries: 0,
        retryWrites: true,
        retryReads: true
      };

      logger.info('Attempting to connect to MongoDB...', {
        uri: mongoUri.replace(/\/\/.*@/, '//***:***@'), // Hide credentials in logs
        database: config.mongodb.dbName
      });

      await mongoose.connect(mongoUri, connectionOptions);

      this.isConnected = true;

      logger.info('MongoDB connected successfully', {
        uri: mongoUri.replace(/\/\/.*@/, '//***:***@'), // Hide credentials in logs
        database: config.mongodb.dbName,
        readyState: mongoose.connection.readyState
      });

      // Handle connection events
      mongoose.connection.on('error', (error) => {
        logger.error('MongoDB connection error', { error: error.message });
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
        this.isConnected = true;
      });

    } catch (error: any) {
      this.isConnected = false;

      // Provide more specific error messages
      let errorMessage = error.message;
      if (error.message.includes('Authentication failed')) {
        errorMessage = 'MongoDB authentication failed. Please check your username and password.';
      } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
        errorMessage = 'Cannot connect to MongoDB server. Please check if MongoDB is running and the URI is correct.';
      } else if (error.message.includes('bad auth')) {
        errorMessage = 'Invalid MongoDB credentials. Please verify your username and password.';
      }

      logger.error('Failed to connect to MongoDB', {
        error: errorMessage,
        originalError: error.message,
        uri: config.mongodb?.uri?.replace(/\/\/.*@/, '//***:***@') || 'N/A' // Hide credentials
      });

      // Don't throw error in development to allow app to start
      if (config.nodeEnv === 'development') {
        logger.warn('MongoDB connection failed in development mode - app will continue without database features');
        return;
      }

      throw new Error(`MongoDB connection failed: ${errorMessage}`);
    }
  }

  /**
   * Disconnect from MongoDB
   */
  public async disconnect(): Promise<void> {
    try {
      if (!this.isConnected) {
        logger.info('MongoDB already disconnected');
        return;
      }

      await mongoose.disconnect();
      this.isConnected = false;
      
      logger.info('MongoDB disconnected successfully');
    } catch (error: any) {
      logger.error('Failed to disconnect from MongoDB', {
        error: error.message
      });
      throw new Error(`MongoDB disconnection failed: ${error.message}`);
    }
  }

  /**
   * Check if database is connected
   */
  public isDbConnected(): boolean {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    readyState: number;
    host?: string;
    name?: string;
  } {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      name: mongoose.connection.name
    };
  }

  /**
   * Health check for database
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      readyState: number;
      responseTime?: number;
      error?: string;
    };
  }> {
    try {
      const startTime = Date.now();
      
      if (!this.isDbConnected()) {
        return {
          status: 'unhealthy',
          details: {
            connected: false,
            readyState: mongoose.connection.readyState,
            error: 'Database not connected'
          }
        };
      }

      // Simple ping to test connection
      await mongoose.connection.db.admin().ping();
      
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        details: {
          connected: true,
          readyState: mongoose.connection.readyState,
          responseTime
        }
      };
    } catch (error: any) {
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          readyState: mongoose.connection.readyState,
          error: error.message
        }
      };
    }
  }

  /**
   * Create database indexes
   */
  public async createIndexes(): Promise<void> {
    try {
      logger.info('Creating database indexes...');
      
      // Indexes will be created automatically by Mongoose schemas
      // This method can be used for custom indexes if needed
      
      logger.info('Database indexes created successfully');
    } catch (error: any) {
      logger.error('Failed to create database indexes', {
        error: error.message
      });
      throw new Error(`Index creation failed: ${error.message}`);
    }
  }

  /**
   * Drop database (use with caution!)
   */
  public async dropDatabase(): Promise<void> {
    try {
      if (!this.isDbConnected()) {
        throw new Error('Database not connected');
      }

      await mongoose.connection.db.dropDatabase();
      logger.warn('Database dropped successfully');
    } catch (error: any) {
      logger.error('Failed to drop database', {
        error: error.message
      });
      throw new Error(`Database drop failed: ${error.message}`);
    }
  }

  /**
   * Get database statistics
   */
  public async getStats(): Promise<{
    collections: number;
    documents: number;
    dataSize: number;
    indexSize: number;
  }> {
    try {
      if (!this.isDbConnected()) {
        throw new Error('Database not connected');
      }

      const stats = await mongoose.connection.db.stats();
      
      return {
        collections: stats.collections,
        documents: stats.objects,
        dataSize: stats.dataSize,
        indexSize: stats.indexSize
      };
    } catch (error: any) {
      logger.error('Failed to get database stats', {
        error: error.message
      });
      return {
        collections: 0,
        documents: 0,
        dataSize: 0,
        indexSize: 0
      };
    }
  }
}

export default DatabaseService;
