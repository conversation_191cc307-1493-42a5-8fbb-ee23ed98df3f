import { ProcessingError } from '../types';
import { config } from '../config';
import logger from './logger';

export interface RetryOptions {
  maxAttempts?: number;
  delayMs?: number;
  backoffMultiplier?: number;
  maxDelayMs?: number;
  retryableErrors?: string[];
}

export class RetryManager {
  private maxAttempts: number;
  private delayMs: number;
  private backoffMultiplier: number;
  private maxDelayMs: number;
  private retryableErrors: string[];

  constructor(options: RetryOptions = {}) {
    this.maxAttempts = options.maxAttempts || config.maxRetryAttempts;
    this.delayMs = options.delayMs || config.retryDelayMs;
    this.backoffMultiplier = options.backoffMultiplier || 2;
    this.maxDelayMs = options.maxDelayMs || 30000; // 30 seconds max
    this.retryableErrors = options.retryableErrors || [
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'RATE_LIMIT_ERROR',
      'TEMPORARY_ERROR',
      'SERVICE_UNAVAILABLE'
    ];
  }

  /**
   * Execute a function with retry logic
   */
  public async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string,
    errorHandler?: (error: any, attempt: number) => ProcessingError
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= this.maxAttempts; attempt++) {
      try {
        const result = await operation();
        
        if (attempt > 1) {
          logger.info('Operation succeeded after retry', {
            context,
            attempt,
            totalAttempts: this.maxAttempts
          });
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        const processingError = errorHandler ? 
          errorHandler(error, attempt) : 
          this.createProcessingError(error);

        logger.warn('Operation failed, checking if retryable', {
          context,
          attempt,
          maxAttempts: this.maxAttempts,
          error: processingError.message,
          retryable: processingError.retryable
        });

        // Don't retry if error is not retryable or we've reached max attempts
        if (!processingError.retryable || attempt >= this.maxAttempts) {
          logger.error('Operation failed permanently', {
            context,
            attempt,
            error: processingError.message,
            retryable: processingError.retryable
          });
          throw error;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.delayMs * Math.pow(this.backoffMultiplier, attempt - 1),
          this.maxDelayMs
        );

        logger.info('Retrying operation after delay', {
          context,
          attempt,
          nextAttempt: attempt + 1,
          delayMs: delay
        });

        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * Create a ProcessingError from any error
   */
  private createProcessingError(error: any): ProcessingError {
    if (error && typeof error === 'object' && 'code' in error) {
      return {
        code: error.code,
        message: error.message || 'Unknown error',
        details: error.details,
        retryable: this.isRetryableError(error.code)
      };
    }

    // Handle common error types
    if (error instanceof Error) {
      let code = 'UNKNOWN_ERROR';
      let retryable = false;

      // Network errors
      if (error.message.includes('ECONNREFUSED') || 
          error.message.includes('ENOTFOUND') ||
          error.message.includes('ETIMEDOUT')) {
        code = 'NETWORK_ERROR';
        retryable = true;
      }
      // Rate limiting
      else if (error.message.includes('rate limit') || 
               error.message.includes('429')) {
        code = 'RATE_LIMIT_ERROR';
        retryable = true;
      }
      // Service unavailable
      else if (error.message.includes('503') || 
               error.message.includes('502') ||
               error.message.includes('504')) {
        code = 'SERVICE_UNAVAILABLE';
        retryable = true;
      }
      // Timeout errors
      else if (error.message.includes('timeout')) {
        code = 'TIMEOUT_ERROR';
        retryable = true;
      }

      return {
        code,
        message: error.message,
        retryable
      };
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: String(error),
      retryable: false
    };
  }

  /**
   * Check if an error code is retryable
   */
  private isRetryableError(errorCode: string): boolean {
    return this.retryableErrors.includes(errorCode);
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a retry manager with email-specific settings
   */
  public static forEmail(): RetryManager {
    return new RetryManager({
      maxAttempts: 3,
      delayMs: 2000,
      retryableErrors: [
        'NETWORK_ERROR',
        'TIMEOUT_ERROR',
        'RATE_LIMIT_ERROR',
        'TEMPORARY_ERROR',
        'SERVICE_UNAVAILABLE',
        'AUTH_ERROR' // Gmail auth might need refresh
      ]
    });
  }

  /**
   * Create a retry manager with Shopify-specific settings
   */
  public static forShopify(): RetryManager {
    return new RetryManager({
      maxAttempts: 5,
      delayMs: 1000,
      retryableErrors: [
        'NETWORK_ERROR',
        'TIMEOUT_ERROR',
        'RATE_LIMIT_ERROR',
        'TEMPORARY_ERROR',
        'SERVICE_UNAVAILABLE',
        'THROTTLED'
      ]
    });
  }

  /**
   * Create a retry manager with logging-specific settings
   */
  public static forLogging(): RetryManager {
    return new RetryManager({
      maxAttempts: 3,
      delayMs: 1000,
      retryableErrors: [
        'NETWORK_ERROR',
        'TIMEOUT_ERROR',
        'RATE_LIMIT_ERROR',
        'TEMPORARY_ERROR',
        'SERVICE_UNAVAILABLE'
      ]
    });
  }
}
