import express, { Request, Response } from 'express';
import { LoggingService } from '../services/LoggingService';
import logger from '../utils/logger';
import fs from 'fs';
import path from 'path';

const router = express.Router();
const loggingService = new LoggingService();

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'success' | 'warning' | 'error';
  message: string;
  details?: any;
}

let recentLogs: LogEntry[] = [];
let logStats = {
  totalProcessed: 0,
  todayProcessed: 0,
  pendingReturns: 0,
  failedReturns: 0,
  lastUpdated: new Date().toISOString()
};

/**
 * Add a log entry to the in-memory store
 */
export function addLogEntry(level: LogEntry['level'], message: string, details?: any): void {
  const logEntry: LogEntry = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    timestamp: new Date().toISOString(),
    level,
    message,
    details
  };

  recentLogs.unshift(logEntry); // Add to beginning
  
  // Keep only last 100 entries
  if (recentLogs.length > 100) {
    recentLogs = recentLogs.slice(0, 100);
  }

  // Update stats based on log level
  updateStatsFromLog(level, message);

  // Also log to Winston for file logging
  logger[level === 'success' ? 'info' : level](message, details);
}

/**
 * Update statistics based on log entries
 */
function updateStatsFromLog(level: LogEntry['level'], message: string): void {
  const today = new Date().toDateString();
  const lastUpdatedDate = new Date(logStats.lastUpdated).toDateString();

  // Reset daily count if it's a new day
  if (today !== lastUpdatedDate) {
    logStats.todayProcessed = 0;
  }

  if (message.includes('processed successfully') || level === 'success') {
    logStats.totalProcessed++;
    logStats.todayProcessed++;
  }

  if (message.includes('failed') || level === 'error') {
    logStats.failedReturns++;
  }

  if (message.includes('pending') || message.includes('waiting')) {
    logStats.pendingReturns++;
  }

  logStats.lastUpdated = new Date().toISOString();
}

/**
 * Get recent logs for dashboard
 */
router.get('/api/logs', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const level = req.query.level as string;

    let filteredLogs = recentLogs;

    // Filter by level if specified
    if (level && ['info', 'success', 'warning', 'error'].includes(level)) {
      filteredLogs = recentLogs.filter(log => log.level === level);
    }

    // Limit results
    const logs = filteredLogs.slice(0, limit);

    res.json({
      success: true,
      logs,
      total: filteredLogs.length,
      stats: logStats
    });
  } catch (error: any) {
    logger.error('Failed to get logs', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get processing statistics
 */
router.get('/api/stats', async (req: Request, res: Response) => {
  try {
    // Get additional stats from log files if available
    const additionalStats = await getLogFileStats();
    
    const combinedStats = {
      ...logStats,
      ...additionalStats,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      stats: combinedStats
    });
  } catch (error: any) {
    logger.error('Failed to get stats', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Export logs to Google Sheets
 */
router.post('/api/export-logs', async (req: Request, res: Response) => {
  try {
    addLogEntry('info', 'Starting log export to Google Sheets...');

    // Create log entries for recent logs
    const logEntries = recentLogs
      .filter(log => log.level === 'success' || log.message.includes('return'))
      .slice(0, 20) // Export last 20 relevant entries
      .map(log => {
        return loggingService.createReturnLogEntry(
          log.id,
          log.details?.orderId || 'N/A',
          log.details?.sku || 'N/A',
          log.details?.supplier || 'Unknown',
          log.details?.qty || 1,
          log.level === 'success' ? 'processed' : 'pending',
          log.details?.customerEmail || 'N/A',
          log.details?.reason || 'Log export',
          'log_export',
          log.level === 'error' ? log.message : undefined
        );
      });

    if (logEntries.length > 0) {
      const result = await loggingService.logReturnEntries(logEntries);
      
      if (result.success) {
        addLogEntry('success', `Successfully exported ${logEntries.length} log entries to Google Sheets`);
        res.json({
          success: true,
          message: `Exported ${logEntries.length} entries to Google Sheets`,
          exported: logEntries.length
        });
      } else {
        throw new Error(result.errors.join(', '));
      }
    } else {
      addLogEntry('warning', 'No relevant log entries found for export');
      res.json({
        success: true,
        message: 'No relevant entries to export',
        exported: 0
      });
    }
  } catch (error: any) {
    addLogEntry('error', `Failed to export logs: ${error.message}`);
    logger.error('Failed to export logs', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Clear recent logs (for dashboard display only)
 */
router.post('/api/clear-logs', async (req: Request, res: Response) => {
  try {
    const previousCount = recentLogs.length;
    recentLogs = [];
    
    addLogEntry('info', `Log display cleared. ${previousCount} entries removed from memory.`);
    
    res.json({
      success: true,
      message: `Cleared ${previousCount} log entries from display`,
      cleared: previousCount
    });
  } catch (error: any) {
    logger.error('Failed to clear logs', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Test logging service configuration
 */
router.get('/api/test-logging', async (req: Request, res: Response) => {
  try {
    addLogEntry('info', 'Testing logging service configuration...');
    
    const testResults = await loggingService.testConfiguration();
    
    addLogEntry('success', `Logging test completed. Google Sheets: ${testResults.googleSheets ? 'OK' : 'FAIL'}, Airtable: ${testResults.airtable ? 'OK' : 'FAIL'}`);
    
    res.json({
      success: true,
      testResults,
      message: 'Logging service test completed'
    });
  } catch (error: any) {
    addLogEntry('error', `Logging service test failed: ${error.message}`);
    logger.error('Failed to test logging service', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get statistics from log files
 */
async function getLogFileStats(): Promise<any> {
  try {
    const logDir = path.join(process.cwd(), 'logs');
    const combinedLogPath = path.join(logDir, 'combined.log');
    const errorLogPath = path.join(logDir, 'error.log');

    const stats: any = {};

    // Count lines in combined log
    if (fs.existsSync(combinedLogPath)) {
      const combinedContent = fs.readFileSync(combinedLogPath, 'utf8');
      stats.totalLogEntries = combinedContent.split('\n').length - 1;
    }

    // Count errors in error log
    if (fs.existsSync(errorLogPath)) {
      const errorContent = fs.readFileSync(errorLogPath, 'utf8');
      stats.totalErrors = errorContent.split('\n').length - 1;
    }

    return stats;
  } catch (error) {
    logger.warn('Could not read log file stats', { error });
    return {};
  }
}

/**
 * Test endpoints for manual testing
 */
router.post('/api/test-return', async (req: Request, res: Response) => {
  try {
    const { simulateReturnProcessing, addTestReturnLog } = await import('../utils/sampleLogs');

    const type = req.body.type || 'simple';

    if (type === 'full') {
      simulateReturnProcessing();
      res.json({
        success: true,
        message: 'Full return processing simulation started'
      });
    } else {
      addTestReturnLog();
      res.json({
        success: true,
        message: 'Test return log added'
      });
    }
  } catch (error: any) {
    logger.error('Failed to add test return', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.post('/api/test-error', async (req: Request, res: Response) => {
  try {
    const { addTestErrorLog } = await import('../utils/sampleLogs');
    addTestErrorLog();

    res.json({
      success: true,
      message: 'Test error log added'
    });
  } catch (error: any) {
    logger.error('Failed to add test error', { error: error.message });
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Initialize with some sample logs
addLogEntry('info', 'Logging service initialized');
addLogEntry('info', 'Dashboard API endpoints ready');

export default router;
