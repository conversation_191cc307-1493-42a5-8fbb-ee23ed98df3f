import { Router, Request, Response } from 'express';
import rateLimit from 'express-rate-limit';
// COMMENTED OUT: MongoDB services - using vendor database JSON instead
// import { MongoSupplierService } from '../services/MongoSupplierService';
// import { DatabaseService } from '../services/DatabaseService';
import logger from '../utils/logger';

const router = Router();
// COMMENTED OUT: MongoDB services - using vendor database JSON instead
// const supplierService = new MongoSupplierService();
// const dbService = DatabaseService.getInstance();

// Import fallback service
import { VendorDatabaseService } from '../services/VendorDatabaseService';
const vendorDbService = new VendorDatabaseService();

// Middleware to check database connection (with fallback)
const checkDatabaseConnection = (req: Request, res: Response, next: any) => {
  // For now, always allow requests and use fallback if MongoDB is not available
  next();
};

// Rate limiting for admin endpoints
const adminRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many admin requests from this IP, please try again later.'
});

router.use(adminRateLimit);
router.use(checkDatabaseConnection);

/**
 * Test endpoint to verify admin routes are working
 */
router.get('/test', (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'Admin routes are working!',
    timestamp: new Date().toISOString(),
    database_type: 'vendor_json', // Using vendor database JSON instead of MongoDB
    mongodb_connected: false // MongoDB is commented out
  });
});

/**
 * Get all suppliers (with MongoDB fallback to JSON)
 */
router.get('/suppliers', async (req: Request, res: Response) => {
  try {
    let suppliers = [];
    let source = 'unknown';

    // COMMENTED OUT: MongoDB usage - using vendor database JSON instead
    // if (dbService.isDbConnected()) {
    //   try {
    //     suppliers = await supplierService.getAllActiveSuppliers();
    //     source = 'mongodb';
    //     logger.info('Suppliers loaded from MongoDB', { count: suppliers.length });
    //   } catch (mongoError: any) {
    //     logger.warn('MongoDB query failed, falling back to JSON', { error: mongoError.message });
    //   }
    // }

    // Use JSON vendor database
    {
      try {
        const vendorData = await vendorDbService.getAllVendors();
        suppliers = Object.entries(vendorData).map(([name, vendor]) => ({
          _id: name.toLowerCase().replace(/\s+/g, '-'),
          name: vendor.name,
          priority: vendor.priority,
          verified: vendor.verified,
          active: true,
          contact: vendor.contact,
          returnPolicy: vendor.returnPolicy,
          products: vendor.products,
          automation: vendor.automation,
          notes: vendor.notes,
          tags: vendor.products?.tags || [],
          vendorNames: [name],
          shopifyVendorNames: [name, vendor.name].filter(Boolean),
          createdAt: new Date(),
          updatedAt: new Date()
        }));
        source = 'json';
        logger.info('Suppliers loaded from JSON fallback', { count: suppliers.length });
      } catch (jsonError: any) {
        logger.error('JSON fallback also failed', { error: jsonError.message });
      }
    }

    res.json({
      success: true,
      data: suppliers,
      count: suppliers.length,
      source: source,
      mongodb_connected: false // MongoDB is commented out
    });
  } catch (error: any) {
    logger.error('Failed to get suppliers', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve suppliers',
      mongodb_connected: false // MongoDB is commented out
    });
  }
});

/**
 * Get supplier by ID
 */
router.get('/suppliers/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const supplier = await supplierService.getSupplierById(id);
    
    if (!supplier) {
      return res.status(404).json({
        success: false,
        error: 'Supplier not found'
      });
    }

    res.json({
      success: true,
      data: supplier
    });
  } catch (error: any) {
    logger.error('Failed to get supplier', { error: error.message, supplierId: req.params.id });
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve supplier'
    });
  }
});

/**
 * Create new supplier
 */
router.post('/suppliers', async (req: Request, res: Response) => {
  try {
    const supplierData = req.body;
    
    // Validate required fields
    if (!supplierData.name || !supplierData.contact?.email) {
      return res.status(400).json({
        success: false,
        error: 'Name and contact email are required'
      });
    }

    const supplier = await supplierService.createSupplier(supplierData);
    
    if (!supplier) {
      return res.status(400).json({
        success: false,
        error: 'Failed to create supplier'
      });
    }

    res.status(201).json({
      success: true,
      data: supplier,
      message: 'Supplier created successfully'
    });
  } catch (error: any) {
    logger.error('Failed to create supplier', { error: error.message, supplierData: req.body });
    res.status(500).json({
      success: false,
      error: 'Failed to create supplier'
    });
  }
});

/**
 * Update supplier
 */
router.put('/suppliers/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const supplier = await supplierService.updateSupplier(id, updateData);
    
    if (!supplier) {
      return res.status(404).json({
        success: false,
        error: 'Supplier not found'
      });
    }

    res.json({
      success: true,
      data: supplier,
      message: 'Supplier updated successfully'
    });
  } catch (error: any) {
    logger.error('Failed to update supplier', { error: error.message, supplierId: req.params.id });
    res.status(500).json({
      success: false,
      error: 'Failed to update supplier'
    });
  }
});

/**
 * Delete supplier (soft delete)
 */
router.delete('/suppliers/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const success = await supplierService.deleteSupplier(id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Supplier not found'
      });
    }

    res.json({
      success: true,
      message: 'Supplier deleted successfully'
    });
  } catch (error: any) {
    logger.error('Failed to delete supplier', { error: error.message, supplierId: req.params.id });
    res.status(500).json({
      success: false,
      error: 'Failed to delete supplier'
    });
  }
});

/**
 * Search suppliers
 */
router.get('/suppliers/search/:term', async (req: Request, res: Response) => {
  try {
    const { term } = req.params;
    const suppliers = await supplierService.searchSuppliers(term);
    
    res.json({
      success: true,
      data: suppliers,
      count: suppliers.length,
      searchTerm: term
    });
  } catch (error: any) {
    logger.error('Failed to search suppliers', { error: error.message, searchTerm: req.params.term });
    res.status(500).json({
      success: false,
      error: 'Failed to search suppliers'
    });
  }
});

/**
 * Get suppliers by vendor name (for webhook processing)
 */
router.get('/suppliers/vendor/:vendorName', async (req: Request, res: Response) => {
  try {
    const { vendorName } = req.params;
    const supplier = await supplierService.getSupplierByVendorName(vendorName);
    
    if (!supplier) {
      return res.status(404).json({
        success: false,
        error: 'No supplier found for vendor name',
        vendorName
      });
    }

    res.json({
      success: true,
      data: supplier,
      vendorName
    });
  } catch (error: any) {
    logger.error('Failed to get supplier by vendor name', { 
      error: error.message, 
      vendorName: req.params.vendorName 
    });
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve supplier by vendor name'
    });
  }
});

/**
 * Get suppliers by tags
 */
router.post('/suppliers/tags', async (req: Request, res: Response) => {
  try {
    const { tags } = req.body;
    
    if (!Array.isArray(tags)) {
      return res.status(400).json({
        success: false,
        error: 'Tags must be an array'
      });
    }

    const suppliers = await supplierService.getSuppliersByTags(tags);
    
    res.json({
      success: true,
      data: suppliers,
      count: suppliers.length,
      tags
    });
  } catch (error: any) {
    logger.error('Failed to get suppliers by tags', { error: error.message, tags: req.body.tags });
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve suppliers by tags'
    });
  }
});

/**
 * Get supplier statistics (with fallback)
 */
router.get('/stats/suppliers', async (req: Request, res: Response) => {
  try {
    let stats = {
      total: 0,
      active: 0,
      verified: 0,
      byPriority: { HIGH: 0, MEDIUM: 0, LOW: 0 }
    };
    let source = 'unknown';

    // COMMENTED OUT: MongoDB usage - using vendor database JSON instead
    // if (dbService.isDbConnected()) {
    //   try {
    //     stats = await supplierService.getSupplierStats();
    //     source = 'mongodb';
    //   } catch (mongoError: any) {
    //     logger.warn('MongoDB stats query failed, falling back to JSON', { error: mongoError.message });
    //   }
    // }

    // Use JSON vendor database
    {
      try {
        const vendorData = await vendorDbService.getAllVendors();
        const vendors = Object.values(vendorData);

        stats = {
          total: vendors.length,
          active: vendors.length, // Assume all JSON vendors are active
          verified: vendors.filter(v => v.verified).length,
          byPriority: {
            HIGH: vendors.filter(v => v.priority === 'HIGH').length,
            MEDIUM: vendors.filter(v => v.priority === 'MEDIUM').length,
            LOW: vendors.filter(v => v.priority === 'LOW').length
          }
        };
        source = 'json';
      } catch (jsonError: any) {
        logger.error('JSON stats fallback failed', { error: jsonError.message });
      }
    }

    res.json({
      success: true,
      data: stats,
      source: source,
      mongodb_connected: false // MongoDB is commented out
    });
  } catch (error: any) {
    logger.error('Failed to get supplier stats', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve supplier statistics',
      mongodb_connected: false // MongoDB is commented out
    });
  }
});

/**
 * Get database health status (JSON vendor database)
 */
router.get('/health/database', async (req: Request, res: Response) => {
  try {
    // Check JSON vendor database health
    const vendorStats = await vendorDbService.getDatabaseStats();

    res.json({
      success: true,
      data: {
        type: 'vendor_json',
        health: 'healthy',
        stats: vendorStats,
        connection: 'connected',
        mongodb_connected: false // MongoDB is commented out
      }
    });
  } catch (error: any) {
    logger.error('Failed to get vendor database health', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve vendor database health',
      mongodb_connected: false // MongoDB is commented out
    });
  }
});

export default router;
