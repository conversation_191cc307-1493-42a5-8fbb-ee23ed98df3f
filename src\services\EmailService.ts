import nodemailer from 'nodemailer';
import { SupplierNotification, EmailData } from '../types';
import { config } from '../config';
import { sanitizeEmailContent } from '../utils/security';
import logger from '../utils/logger';
import { SupplierNotificationService } from './SupplierNotificationService';
import { CustomerNotificationService } from './CustomerNotificationService';

export class EmailService {
  private transporter: nodemailer.Transporter;
  private supplierNotificationService: SupplierNotificationService;
  private customerNotificationService: CustomerNotificationService;

  constructor() {
    this.transporter = this.initializeTransporter();
    this.supplierNotificationService = new SupplierNotificationService();
    this.customerNotificationService = new CustomerNotificationService();
  }

  /**
   * Initialize email transporter using SMTP
   */
  private initializeTransporter(): nodemailer.Transporter {
    try {
      const transporter = nodemailer.createTransport({
        host: config.email.host,
        port: config.email.port,
        secure: config.email.port === 465, // true for 465, false for other ports
        auth: {
          user: config.email.user,
          pass: config.email.pass
        },
        tls: {
          rejectUnauthorized: false
        }
      });

      logger.info('Email transporter initialized successfully', {
        host: config.email.host,
        port: config.email.port,
        secure: config.email.secure
      });

      return transporter;
    } catch (error) {
      logger.error('Failed to initialize email transporter', { error });
      throw new Error('Email service initialization failed');
    }
  }



  /**
   * Send email notification to supplier
   */
  public async sendSupplierNotification(notification: SupplierNotification): Promise<boolean> {
    try {
      // Use the beautiful template from SupplierNotificationService
      // First, we need to convert the notification to the format expected by SupplierNotificationService
      const vendorInfo = {
        name: notification.supplier,
        priority: 'MEDIUM' as const,
        verified: true,
        contact: {
          email: notification.email,
          phone: '',
          address: {
            street: '',
            city: '',
            state: '',
            zip: '',
            country: 'USA'
          },
          website: '',
          businessHours: '9 AM - 5 PM EST',
          timezone: 'EST'
        },
        returnPolicy: {
          timeLimit: '30 days',
          requirements: 'Original packaging required, unused condition',
          restockingFee: '0%',
          returnAddress: '',
          url: ''
        },
        products: {
          categories: [],
          skuPrefixes: [],
          count: 0,
          tags: []
        },
        automation: {
          requiresApproval: notification.requiresManualReview || true,
          autoRefund: false,
          processingTime: '2-3 business days',
          emailTemplate: 'default'
        },
        notes: 'Auto-generated vendor info for email template'
      };

      const notificationData = {
        supplier: notification.supplier,
        email: notification.email,
        returnId: notification.returnId,
        orderId: notification.orderId,
        customerEmail: notification.customerEmail,
        customerName: notification.customerName,
        items: notification.items,
        createdAt: notification.createdAt,
        requiresManualReview: notification.requiresManualReview,
        vendorInfo: vendorInfo
      };

      // Generate the beautiful template
      const template = this.supplierNotificationService.generateEmailTemplate(vendorInfo, notificationData);

      // Environment-based email routing
      let recipientEmail: string;
      let emailSubject: string;

      if (config.nodeEnv === 'development') {
        // Development: Send to support emails
        recipientEmail = '<EMAIL>, <EMAIL>';
        emailSubject = `[DEV SUPPLIER] ${template.subject} - ${notification.supplier}`;

        logger.info('Development mode: Sending supplier notification to support emails', {
          supplier: notification.supplier,
          actualVendorEmail: vendorInfo.contact.email
        });
      } else {
        // Production: Send to actual vendor database email
        recipientEmail = vendorInfo.contact.email;
        emailSubject = template.subject;

        logger.info('Production mode: Sending supplier notification to vendor email', {
          supplier: notification.supplier,
          vendorEmail: vendorInfo.contact.email
        });
      }

      const emailData: EmailData = {
        to: recipientEmail,
        subject: emailSubject,
        html: template.html
      };

      await this.sendEmail(emailData);
      
      logger.info('Supplier notification sent successfully', {
        supplier: notification.supplier,
        email: notification.email,
        returnId: notification.returnId,
        itemCount: notification.items.length
      });

      return true;
    } catch (error) {
      logger.error('Failed to send supplier notification', {
        error,
        supplier: notification.supplier,
        email: notification.email,
        returnId: notification.returnId
      });
      return false;
    }
  }

  /**
   * Send customer notification using beautiful template
   */
  public async sendCustomerNotification(notification: SupplierNotification): Promise<boolean> {
    try {
      // Use the beautiful template from CustomerNotificationService
      const customerData = {
        returnId: notification.returnId,
        orderId: notification.orderId,
        orderNumber: notification.orderNumber || notification.orderId,
        customerEmail: notification.customerEmail,
        customerName: notification.customerName || 'Customer',
        items: notification.items,
        requestType: 'return' as const,
        submittedDate: notification.createdAt,
        estimatedProcessingTime: notification.estimatedProcessingTime,
        trackingInfo: notification.trackingInfo,
        notes: notification.customerNotes
      };

      // Generate the beautiful template
      const template = this.customerNotificationService.generateCustomerEmailTemplate(customerData);

      // Send to actual customer email from webhook data
      const emailData: EmailData = {
        to: notification.customerEmail, // Send to actual customer email
        subject: template.subject,
        html: template.html
      };

      await this.sendEmail(emailData);

      // Also send copy to support for tracking
      const supportCopyData: EmailData = {
        to: '<EMAIL>',
        subject: `[CUSTOMER COPY] ${template.subject} - ${notification.customerName || notification.customerEmail}`,
        html: `
          <div style="background-color: #f0fff0; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="color: #009900; margin: 0;">👤 CUSTOMER NOTIFICATION COPY</h3>
            <p style="margin: 10px 0 0 0; color: #666;">
              This is a copy of the notification sent to customer: <strong>${notification.customerEmail}</strong>
            </p>
          </div>
          ${template.html}
        `
      };

      // Send support copy (don't wait for it)
      this.sendEmail(supportCopyData).catch(error => {
        logger.error('Failed to send customer notification copy to support', {
          error,
          customerEmail: notification.customerEmail,
          returnId: notification.returnId
        });
      });

      logger.info('Customer notification sent successfully', {
        customerEmail: notification.customerEmail,
        returnId: notification.returnId
      });

      return true;
    } catch (error) {
      logger.error('Failed to send customer notification', {
        error,
        customerEmail: notification.customerEmail,
        returnId: notification.returnId
      });
      return false;
    }
  }

  /**
   * Send internal review notification
   */
  public async sendInternalReviewNotification(
    notification: SupplierNotification,
    reason: string = 'Manual review required'
  ): Promise<boolean> {
    try {
      const subject = `Internal Review Required - Return ${notification.returnId}`;
      
      const body = `
        <html>
          <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
              <h2 style="color: #e74c3c;">Internal Review Required</h2>
              
              <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p><strong>Reason:</strong> ${sanitizeEmailContent(reason)}</p>
                <p><strong>Return ID:</strong> ${sanitizeEmailContent(notification.returnId)}</p>
                <p><strong>Supplier:</strong> ${sanitizeEmailContent(notification.supplier)}</p>
                <p><strong>Customer:</strong> ${sanitizeEmailContent(notification.customerEmail)}</p>
              </div>

              <h3>Items Requiring Review:</h3>
              <ul>
                ${notification.items.map(item => `
                  <li>${sanitizeEmailContent(item.sku)} - ${sanitizeEmailContent(item.name)} (Qty: ${item.qty})</li>
                `).join('')}
              </ul>

              <p>Please review this return request in the admin panel.</p>
            </div>
          </body>
        </html>
      `;

      // FOR TESTING: Send to admin and support instead of just internal review email
      const emailData: EmailData = {
        to: '<EMAIL>, <EMAIL>',
        subject: `[TEST INTERNAL] ${subject}`,
        html: body
      };

      await this.sendEmail(emailData);
      
      logger.info('Internal review notification sent', {
        returnId: notification.returnId,
        supplier: notification.supplier
      });

      return true;
    } catch (error) {
      logger.error('Failed to send internal review notification', {
        error,
        returnId: notification.returnId
      });
      return false;
    }
  }

  /**
   * Send email using the configured transporter
   */
  private async sendEmail(emailData: EmailData): Promise<void> {
    if (!this.transporter) {
      throw new Error('Email transporter not initialized');
    }

    const mailOptions = {
      from: config.email.from,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      attachments: emailData.attachments
    };

    await this.transporter.sendMail(mailOptions);
  }

  /**
   * Test email configuration
   */
  public async testEmailConfiguration(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Email configuration test successful');
      return true;
    } catch (error) {
      logger.error('Email configuration test failed', { error });
      return false;
    }
  }
}
