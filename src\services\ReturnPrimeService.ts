import axios, { AxiosInstance } from 'axios';
import { config } from '../config';
import logger from '../utils/logger';

export interface ReturnPrimeOrder {
  order_id: string;
  order_number: string;
  customer_email: string;
  customer_name?: string;
  status: string;
  fulfillment_status: string;
  items: ReturnPrimeOrderItem[];
  created_at: string;
  updated_at: string;
}

export interface ReturnPrimeOrderItem {
  id: string;
  sku: string;
  name: string;
  quantity: number;
  price: string;
  vendor?: string;
  product_id?: string;
  variant_id?: string;
}

export interface ReturnPrimeReturn {
  id: string;
  return_number: string;
  order_id: string;
  order_number: string;
  customer_email: string;
  customer_name?: string;
  status: 'pending' | 'approved' | 'rejected' | 'processing' | 'completed';
  return_type: 'refund' | 'exchange' | 'store_credit';
  items: ReturnPrimeReturnItem[];
  created_at: string;
  updated_at: string;
  notes?: string;
}

export interface ReturnPrimeReturnItem {
  id: string;
  order_item_id: string;
  sku: string;
  name: string;
  quantity: number;
  reason: string;
  condition?: string;
  images?: string[];
  resolution?: string;
}

export interface ReturnPrimeApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export class ReturnPrimeService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.returnPrime.apiUrl,
      headers: {
        'Authorization': `Bearer ${config.returnPrime.adminAccessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('Return Prime API Request', {
          method: config.method,
          url: config.url,
          params: config.params
        });
        return config;
      },
      (error) => {
        logger.error('Return Prime API Request Error', { error });
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('Return Prime API Response', {
          status: response.status,
          url: response.config.url
        });
        return response;
      },
      (error) => {
        logger.error('Return Prime API Response Error', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.response?.data?.message || error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get order details from Return Prime
   */
  public async getOrder(orderNumber: string): Promise<ReturnPrimeApiResponse<ReturnPrimeOrder>> {
    try {
      const response = await this.client.get(`/orders/${orderNumber}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to get order from Return Prime', {
        orderNumber,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get return details from Return Prime
   */
  public async getReturn(returnId: string): Promise<ReturnPrimeApiResponse<ReturnPrimeReturn>> {
    try {
      const response = await this.client.get(`/returns/${returnId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to get return from Return Prime', {
        returnId,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get all returns for a specific order
   */
  public async getOrderReturns(orderNumber: string): Promise<ReturnPrimeApiResponse<ReturnPrimeReturn[]>> {
    try {
      const response = await this.client.get(`/orders/${orderNumber}/returns`);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to get order returns from Return Prime', {
        orderNumber,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get fulfilled order status (used for checking if returns are available)
   */
  public async getFulfilledOrderStatus(orderNumbers: string[], storeName: string): Promise<ReturnPrimeApiResponse<any>> {
    try {
      const response = await this.client.post('/get-fulfilled-order-status', {
        order_numbers: orderNumbers,
        store_name: storeName
      });
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to get fulfilled order status from Return Prime', {
        orderNumbers,
        storeName,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Create a return request in Return Prime
   */
  public async createReturn(returnData: {
    order_number: string;
    customer_email: string;
    items: Array<{
      sku: string;
      quantity: number;
      reason: string;
      condition?: string;
      images?: string[];
    }>;
    return_type: 'refund' | 'exchange' | 'store_credit';
    notes?: string;
  }): Promise<ReturnPrimeApiResponse<ReturnPrimeReturn>> {
    try {
      const response = await this.client.post('/returns', returnData);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to create return in Return Prime', {
        returnData,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Update return status in Return Prime
   */
  public async updateReturnStatus(
    returnId: string, 
    status: 'approved' | 'rejected' | 'processing' | 'completed',
    notes?: string
  ): Promise<ReturnPrimeApiResponse<ReturnPrimeReturn>> {
    try {
      const response = await this.client.patch(`/returns/${returnId}`, {
        status,
        notes
      });
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to update return status in Return Prime', {
        returnId,
        status,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get all returns with pagination
   */
  public async getAllReturns(params?: {
    page?: number;
    limit?: number;
    status?: string;
    order_number?: string;
    customer_email?: string;
    date_from?: string;
    date_to?: string;
  }): Promise<ReturnPrimeApiResponse<{
    returns: ReturnPrimeReturn[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }>> {
    try {
      const response = await this.client.get('/returns', { params });
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to get all returns from Return Prime', {
        params,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Fetch order data for return processing (similar to the frontend API)
   */
  public async fetchOrder(orderNumber: string, customerEmail: string, storeName: string): Promise<ReturnPrimeApiResponse<any>> {
    try {
      const response = await this.client.post('/fetch_order', {
        order_number: orderNumber,
        email: customerEmail,
        store: storeName
      });
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to fetch order from Return Prime', {
        orderNumber,
        customerEmail,
        storeName,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get return statistics for reporting
   */
  public async getReturnStatistics(params?: {
    date_from?: string;
    date_to?: string;
    store_name?: string;
  }): Promise<ReturnPrimeApiResponse<{
    total_returns: number;
    approved_returns: number;
    pending_returns: number;
    rejected_returns: number;
    total_refund_amount: number;
    average_processing_time: number;
  }>> {
    try {
      const response = await this.client.get('/returns/statistics', { params });
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Failed to get return statistics from Return Prime', {
        params,
        error: error.response?.data || error.message
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Test API connection
   */
  public async testConnection(): Promise<boolean> {
    try {
      // Try to get a simple endpoint to test connection
      await this.client.get('/health');
      logger.info('Return Prime API connection test successful');
      return true;
    } catch (error: any) {
      // If health endpoint doesn't exist, try getting returns with limit 1
      try {
        await this.client.get('/returns', { params: { limit: 1 } });
        logger.info('Return Prime API connection test successful (via returns endpoint)');
        return true;
      } catch (fallbackError: any) {
        logger.error('Return Prime API connection test failed', {
          error: fallbackError.response?.data || fallbackError.message
        });
        return false;
      }
    }
  }
}
