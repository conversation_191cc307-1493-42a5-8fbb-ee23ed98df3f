const axios = require('axios');
require('dotenv').config();

const API_KEY = process.env.RENDER_API_KEY;
const BASE_URL = 'https://api.render.com/v1';

async function testRenderApiDirect() {
  console.log('🧪 Testing Render API directly...\n');

  if (!API_KEY) {
    console.log('❌ RENDER_API_KEY not found in environment variables');
    return;
  }

  console.log('✅ API Key found:', API_KEY.substring(0, 10) + '...');

  const axiosInstance = axios.create({
    baseURL: BASE_URL,
    headers: {
      'Authorization': `Bearer ${API_KEY}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
    timeout: 10000
  });

  try {
    // Test 1: List services
    console.log('1. Testing services endpoint...');
    const servicesResponse = await axiosInstance.get('/services');
    console.log('✅ Services response status:', servicesResponse.status);
    console.log('   Response data type:', typeof servicesResponse.data);
    console.log('   Response data:', JSON.stringify(servicesResponse.data, null, 2));
    console.log('');

    // Test 2: List logs (without filters)
    console.log('2. Testing logs endpoint (no filters)...');
    try {
      const logsResponse = await axiosInstance.get('/logs');
      console.log('✅ Logs response status:', logsResponse.status);
      console.log('   Response data:', JSON.stringify(logsResponse.data, null, 2));
    } catch (error) {
      console.log('⚠️  Logs endpoint failed:', error.response?.status, error.response?.data);
    }
    console.log('');

    // Test 3: List logs with time range
    console.log('3. Testing logs endpoint with time range...');
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      const logsResponse = await axiosInstance.get('/logs', {
        params: {
          startTime: oneHourAgo.toISOString(),
          endTime: now.toISOString(),
          limit: 10
        }
      });
      console.log('✅ Logs with time range response status:', logsResponse.status);
      console.log('   Response data:', JSON.stringify(logsResponse.data, null, 2));
    } catch (error) {
      console.log('⚠️  Logs with time range failed:', error.response?.status, error.response?.data);
    }
    console.log('');

    // Test 4: If we have services, try to get logs for a specific service
    if (servicesResponse.data && Array.isArray(servicesResponse.data) && servicesResponse.data.length > 0) {
      const firstService = servicesResponse.data[0];
      console.log(`4. Testing logs for specific service: ${firstService.name} (${firstService.id})...`);
      
      try {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        
        const serviceLogsResponse = await axiosInstance.get('/logs', {
          params: {
            resourceId: firstService.id,
            startTime: oneHourAgo.toISOString(),
            endTime: now.toISOString(),
            limit: 10
          }
        });
        console.log('✅ Service logs response status:', serviceLogsResponse.status);
        console.log('   Response data:', JSON.stringify(serviceLogsResponse.data, null, 2));
      } catch (error) {
        console.log('⚠️  Service logs failed:', error.response?.status, error.response?.data);
      }
    } else {
      console.log('4. No services found to test service-specific logs');
    }
    console.log('');

    console.log('🎉 Direct API test complete!');

  } catch (error) {
    console.error('❌ Direct API test failed:', error.response?.status, error.response?.data || error.message);
  }
}

// Run the test
testRenderApiDirect();
