import * as fs from 'fs';
import * as path from 'path';
import logger from '../utils/logger';

export interface VendorContact {
  email: string;
  phone?: string;
  website?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  businessHours?: string;
  timezone?: string;
}

export interface VendorReturnPolicy {
  url?: string;
  timeLimit?: string;
  requirements?: string;
  restockingFee?: string;
  returnAddress?: string;
}

export interface VendorProducts {
  count?: number;
  categories?: string[];
  tags?: string[];
}

export interface VendorAutomation {
  emailTemplate?: string;
  requiresApproval: boolean;
  autoRefund: boolean;
  processingTime: string;
}

export interface VendorInfo {
  name: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  verified: boolean;
  contact: VendorContact;
  returnPolicy?: VendorReturnPolicy;
  products?: VendorProducts;
  automation: VendorAutomation;
  notes?: string;
}

export interface VendorDatabase {
  lastUpdated: string;
  totalVendors: number;
  vendors: Record<string, VendorInfo>;
}

export class VendorDatabaseService {
  private databasePath: string;
  private cache: VendorDatabase | null = null;
  private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes
  private lastCacheTime: number = 0;

  constructor(databasePath?: string) {
    this.databasePath = databasePath || path.join(process.cwd(), 'vendor-database.json');
  }

  /**
   * Load vendor database from file with caching
   */
  private async loadDatabase(): Promise<VendorDatabase> {
    const now = Date.now();
    
    // Return cached data if still valid
    if (this.cache && (now - this.lastCacheTime) < this.cacheExpiry) {
      return this.cache;
    }

    try {
      if (!fs.existsSync(this.databasePath)) {
        logger.warn('Vendor database file not found, creating empty database', {
          path: this.databasePath
        });
        
        const emptyDatabase: VendorDatabase = {
          lastUpdated: new Date().toISOString(),
          totalVendors: 0,
          vendors: {}
        };
        
        await this.saveDatabase(emptyDatabase);
        return emptyDatabase;
      }

      const fileContent = fs.readFileSync(this.databasePath, 'utf8');
      const database: VendorDatabase = JSON.parse(fileContent);
      
      // Update cache
      this.cache = database;
      this.lastCacheTime = now;
      
      logger.debug('Vendor database loaded successfully', {
        totalVendors: database.totalVendors,
        lastUpdated: database.lastUpdated
      });
      
      return database;
    } catch (error) {
      logger.error('Failed to load vendor database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        path: this.databasePath
      });
      throw new Error(`Failed to load vendor database: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save vendor database to file
   */
  private async saveDatabase(database: VendorDatabase): Promise<void> {
    try {
      database.lastUpdated = new Date().toISOString();
      database.totalVendors = Object.keys(database.vendors).length;
      
      const fileContent = JSON.stringify(database, null, 2);
      fs.writeFileSync(this.databasePath, fileContent, 'utf8');
      
      // Update cache
      this.cache = database;
      this.lastCacheTime = Date.now();
      
      logger.debug('Vendor database saved successfully', {
        totalVendors: database.totalVendors,
        path: this.databasePath
      });
    } catch (error) {
      logger.error('Failed to save vendor database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        path: this.databasePath
      });
      throw new Error(`Failed to save vendor database: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get vendor information by name
   */
  public async getVendor(vendorName: string): Promise<VendorInfo | null> {
    try {
      const database = await this.loadDatabase();
      const vendor = database.vendors[vendorName];
      
      if (vendor) {
        logger.debug('Vendor found in database', {
          vendorName,
          verified: vendor.verified,
          priority: vendor.priority
        });
      } else {
        logger.debug('Vendor not found in database', { vendorName });
      }
      
      return vendor || null;
    } catch (error) {
      logger.error('Failed to get vendor from database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        vendorName
      });
      return null;
    }
  }

  /**
   * Get all vendors
   */
  public async getAllVendors(): Promise<Record<string, VendorInfo>> {
    try {
      const database = await this.loadDatabase();
      return database.vendors;
    } catch (error) {
      logger.error('Failed to get all vendors from database', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return {};
    }
  }

  /**
   * Add or update vendor information
   */
  public async upsertVendor(vendorName: string, vendorInfo: VendorInfo): Promise<boolean> {
    try {
      const database = await this.loadDatabase();
      
      const isUpdate = vendorName in database.vendors;
      database.vendors[vendorName] = vendorInfo;
      
      await this.saveDatabase(database);
      
      logger.info(`Vendor ${isUpdate ? 'updated' : 'added'} successfully`, {
        vendorName,
        verified: vendorInfo.verified,
        priority: vendorInfo.priority
      });
      
      return true;
    } catch (error) {
      logger.error('Failed to upsert vendor in database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        vendorName
      });
      return false;
    }
  }

  /**
   * Remove vendor from database
   */
  public async removeVendor(vendorName: string): Promise<boolean> {
    try {
      const database = await this.loadDatabase();
      
      if (!(vendorName in database.vendors)) {
        logger.warn('Vendor not found for removal', { vendorName });
        return false;
      }
      
      delete database.vendors[vendorName];
      await this.saveDatabase(database);
      
      logger.info('Vendor removed successfully', { vendorName });
      return true;
    } catch (error) {
      logger.error('Failed to remove vendor from database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        vendorName
      });
      return false;
    }
  }

  /**
   * Get vendors by priority
   */
  public async getVendorsByPriority(priority: 'HIGH' | 'MEDIUM' | 'LOW'): Promise<Record<string, VendorInfo>> {
    try {
      const allVendors = await this.getAllVendors();
      const filteredVendors: Record<string, VendorInfo> = {};
      
      for (const [name, vendor] of Object.entries(allVendors)) {
        if (vendor.priority === priority) {
          filteredVendors[name] = vendor;
        }
      }
      
      return filteredVendors;
    } catch (error) {
      logger.error('Failed to get vendors by priority', {
        error: error instanceof Error ? error.message : 'Unknown error',
        priority
      });
      return {};
    }
  }

  /**
   * Get verified vendors only
   */
  public async getVerifiedVendors(): Promise<Record<string, VendorInfo>> {
    try {
      const allVendors = await this.getAllVendors();
      const verifiedVendors: Record<string, VendorInfo> = {};
      
      for (const [name, vendor] of Object.entries(allVendors)) {
        if (vendor.verified) {
          verifiedVendors[name] = vendor;
        }
      }
      
      return verifiedVendors;
    } catch (error) {
      logger.error('Failed to get verified vendors', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return {};
    }
  }

  /**
   * Search vendors by name (case-insensitive partial match)
   */
  public async searchVendors(searchTerm: string): Promise<Record<string, VendorInfo>> {
    try {
      const allVendors = await this.getAllVendors();
      const matchingVendors: Record<string, VendorInfo> = {};
      const lowerSearchTerm = searchTerm.toLowerCase();
      
      for (const [name, vendor] of Object.entries(allVendors)) {
        if (name.toLowerCase().includes(lowerSearchTerm)) {
          matchingVendors[name] = vendor;
        }
      }
      
      return matchingVendors;
    } catch (error) {
      logger.error('Failed to search vendors', {
        error: error instanceof Error ? error.message : 'Unknown error',
        searchTerm
      });
      return {};
    }
  }

  /**
   * Clear cache (useful for testing or forcing refresh)
   */
  public clearCache(): void {
    this.cache = null;
    this.lastCacheTime = 0;
    logger.debug('Vendor database cache cleared');
  }

  /**
   * Get database statistics
   */
  public async getDatabaseStats(): Promise<{
    totalVendors: number;
    verifiedVendors: number;
    highPriorityVendors: number;
    lastUpdated: string;
  }> {
    try {
      const database = await this.loadDatabase();
      const vendors = Object.values(database.vendors);
      
      return {
        totalVendors: vendors.length,
        verifiedVendors: vendors.filter(v => v.verified).length,
        highPriorityVendors: vendors.filter(v => v.priority === 'HIGH').length,
        lastUpdated: database.lastUpdated
      };
    } catch (error) {
      logger.error('Failed to get database stats', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return {
        totalVendors: 0,
        verifiedVendors: 0,
        highPriorityVendors: 0,
        lastUpdated: 'Unknown'
      };
    }
  }
}
