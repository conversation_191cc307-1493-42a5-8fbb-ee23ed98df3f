/**
 * Utility to add sample log entries for testing the dashboard
 */

import { addLogEntry } from '../routes/logs';

export function addSampleLogs(): void {
  // Add some initial sample logs to demonstrate the dashboard
  setTimeout(() => {
    addLogEntry('info', 'Return processing service started successfully');
    addLogEntry('info', 'Monitoring Return Prime webhooks...');
    addLogEntry('info', 'Google Sheets integration active');
  }, 1000);

  setTimeout(() => {
    addLogEntry('info', 'Return Prime webhook received for return RP-2024-001', {
      returnId: 'RP-2024-001',
      orderId: 'ORDER-12345',
      customerEmail: '<EMAIL>',
      itemCount: 2
    });
  }, 3000);

  setTimeout(() => {
    addLogEntry('success', 'Supplier SmokeDrop notified successfully', {
      vendor: 'SmokeDrop',
      returnId: 'RP-2024-001',
      email: '<EMAIL>',
      itemCount: 1
    });
  }, 5000);

  setTimeout(() => {
    addLogEntry('success', 'Supplier Buddify notified successfully', {
      vendor: 'Buddify',
      returnId: 'RP-2024-001',
      email: '<EMAIL>',
      itemCount: 1
    });
  }, 6000);

  setTimeout(() => {
    addLogEntry('success', 'Return RP-2024-001 processed successfully', {
      returnId: 'RP-2024-001',
      orderId: 'ORDER-12345',
      processedItems: 2,
      notificationsSent: 2,
      customerEmail: '<EMAIL>'
    });
  }, 7000);

  setTimeout(() => {
    addLogEntry('info', 'Return data logged to Google Sheets', {
      returnId: 'RP-2024-001',
      supplier: 'SmokeDrop, Buddify',
      status: 'processed'
    });
  }, 8000);

  // Add periodic sample logs to keep the dashboard active
  setInterval(() => {
    const sampleEvents = [
      {
        level: 'info' as const,
        message: 'System health check completed',
        details: { status: 'healthy', uptime: process.uptime() }
      },
      {
        level: 'info' as const,
        message: 'Monitoring for new Return Prime webhooks...',
        details: { activeConnections: 1 }
      },
      {
        level: 'success' as const,
        message: 'Google Sheets connection verified',
        details: { lastSync: new Date().toISOString() }
      }
    ];

    // Randomly add a sample event (10% chance every 30 seconds)
    if (Math.random() < 0.1) {
      const event = sampleEvents[Math.floor(Math.random() * sampleEvents.length)];
      addLogEntry(event.level, event.message, event.details);
    }
  }, 30000); // Every 30 seconds
}

export function addTestReturnLog(returnId: string = 'TEST-' + Date.now()): void {
  const orderId = 'ORDER-' + Math.floor(Math.random() * 10000);
  const suppliers = ['SmokeDrop', 'Buddify', 'Canna River', 'Discreet Smoker'];
  const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];
  
  addLogEntry('info', `Test return webhook received for return ${returnId}`, {
    returnId,
    orderId,
    customerEmail: '<EMAIL>',
    itemCount: Math.floor(Math.random() * 3) + 1
  });

  setTimeout(() => {
    addLogEntry('success', `Supplier ${supplier} notified successfully`, {
      vendor: supplier,
      returnId,
      email: `returns@${supplier.toLowerCase().replace(' ', '')}.com`,
      itemCount: 1
    });
  }, 2000);

  setTimeout(() => {
    addLogEntry('success', `Return ${returnId} processed successfully`, {
      returnId,
      orderId,
      processedItems: 1,
      notificationsSent: 1,
      customerEmail: '<EMAIL>'
    });
  }, 3000);
}

export function addTestErrorLog(): void {
  const errorMessages = [
    'Failed to connect to Shopify API',
    'Supplier email delivery failed',
    'Google Sheets write timeout',
    'Invalid webhook signature',
    'Return Prime API rate limit exceeded'
  ];

  const error = errorMessages[Math.floor(Math.random() * errorMessages.length)];
  
  addLogEntry('error', `Test error: ${error}`, {
    error,
    timestamp: new Date().toISOString(),
    retryable: true
  });
}

export function simulateReturnProcessing(): void {
  console.log('🎭 Starting return processing simulation...');
  
  // Simulate a complete return processing workflow
  const returnId = 'SIM-' + Date.now();
  const orderId = 'ORDER-' + Math.floor(Math.random() * 10000);
  
  addLogEntry('info', `Simulated Return Prime webhook received for return ${returnId}`, {
    returnId,
    orderId,
    customerEmail: '<EMAIL>',
    itemCount: 2,
    eventType: 'request.created'
  });

  setTimeout(() => {
    addLogEntry('info', 'Fetching product details from Shopify...', {
      returnId,
      productIds: ['12345', '67890']
    });
  }, 1000);

  setTimeout(() => {
    addLogEntry('info', 'Mapping products to suppliers...', {
      returnId,
      mappedSuppliers: ['SmokeDrop', 'Buddify']
    });
  }, 2000);

  setTimeout(() => {
    addLogEntry('success', 'Supplier SmokeDrop notified successfully', {
      vendor: 'SmokeDrop',
      returnId,
      email: '<EMAIL>',
      itemCount: 1
    });
  }, 3000);

  setTimeout(() => {
    addLogEntry('success', 'Supplier Buddify notified successfully', {
      vendor: 'Buddify',
      returnId,
      email: '<EMAIL>',
      itemCount: 1
    });
  }, 4000);

  setTimeout(() => {
    addLogEntry('info', 'Logging return data to Google Sheets...', {
      returnId,
      dataPoints: 16
    });
  }, 5000);

  setTimeout(() => {
    addLogEntry('success', `Return ${returnId} processing completed successfully`, {
      returnId,
      orderId,
      processedItems: 2,
      notificationsSent: 2,
      customerEmail: '<EMAIL>',
      processingTime: '5.2s'
    });
  }, 6000);

  console.log(`✅ Simulated return processing for ${returnId}`);
}
