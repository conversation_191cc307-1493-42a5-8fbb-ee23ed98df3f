# Render Service Integration

This integration allows you to monitor your Render services and view their logs directly from your Shopify automation dashboard.

## Features

- 🚀 **Service Monitoring**: View all your Render services with their status, type, and region
- 📊 **Real-time Logs**: Access and search through service logs with filtering options
- 🔄 **Auto-refresh**: Automatically refresh logs every 10 seconds
- 🎯 **Service-specific Logs**: Filter logs by specific services
- 🔍 **Log Search**: Search through logs with text queries
- 📱 **Responsive Dashboard**: Clean, modern interface that works on all devices

## Setup

### 1. Get Your Render API Key

1. Go to [Render Dashboard](https://dashboard.render.com/)
2. Navigate to Account Settings → API Keys
3. Create a new API key or copy an existing one

### 2. Configure Environment Variables

Add your Render API key to your `.env` file:

```env
RENDER_API_KEY=your_render_api_key_here
```

### 3. Restart Your Server

```bash
npm run dev
```

## Usage

### Dashboard Access

Visit the Render dashboard at: `http://localhost:3000/render-dashboard`

### API Endpoints

#### Test Connection
```
GET /render/test-connection
```

#### Update API Key
```
POST /render/update-api-key
Content-Type: application/json

{
  "apiKey": "your_new_api_key"
}
```

#### Get All Services
```
GET /render/services
```

#### Get Specific Service
```
GET /render/services/:serviceId
```

#### Get Logs
```
GET /render/logs?resourceIds=service1,service2&level=error&search=query&limit=100
```

#### Get Service-specific Logs
```
GET /render/services/:serviceId/logs?level=info&limit=50
```

### Query Parameters for Logs

- `resourceIds`: Comma-separated list of service IDs to filter logs
- `startTime`: ISO timestamp for log start time
- `endTime`: ISO timestamp for log end time
- `limit`: Maximum number of logs to return (default: 100)
- `direction`: 'forward' or 'backward' (default: 'backward')
- `level`: Log level filter ('info', 'warn', 'error')
- `search`: Text search query

## Dashboard Features

### Service Cards
- Click on any service card to automatically filter logs for that service
- View service status, type, region, and creation date
- Color-coded status indicators

### Log Filtering
- **Service Filter**: Select specific services from dropdown
- **Log Level Filter**: Filter by info, warning, or error logs
- **Text Search**: Search through log messages
- **Auto Refresh**: Toggle automatic log refreshing

### Real-time Monitoring
- Enable auto-refresh to monitor logs in real-time
- Logs refresh every 10 seconds when enabled
- Visual indicators show when data is loading

## Testing

Run the test script to verify the integration:

```bash
node test-render-service.js
```

This will test:
- Health check endpoints
- Service connectivity
- Error handling without API key
- Dashboard accessibility
- API key validation

## Troubleshooting

### Common Issues

1. **"Render API service not available"**
   - Check that `RENDER_API_KEY` is set in your `.env` file
   - Verify the API key is valid
   - Restart your server after adding the key

2. **"Failed to fetch services"**
   - Verify your API key has the correct permissions
   - Check your internet connection
   - Ensure the API key hasn't expired

3. **Dashboard not loading**
   - Make sure the server is running on port 3000
   - Check browser console for JavaScript errors
   - Verify the `/render-dashboard` route is accessible

4. **No logs appearing**
   - Check if your services are generating logs
   - Verify the service IDs are correct
   - Try adjusting the time range or removing filters

### API Key Permissions

Your Render API key needs the following permissions:
- Read access to services
- Read access to logs

### Rate Limiting

The Render API has rate limits. If you encounter rate limiting:
- Reduce the frequency of auto-refresh
- Limit the number of logs requested
- Use more specific time ranges

## Integration with Existing System

The Render service integrates seamlessly with your existing logging system:

- All Render API calls are logged through your Winston logger
- Dashboard activities are tracked in your application logs
- Errors are properly handled and logged
- Status updates appear in your main logs dashboard

## Security

- API keys are handled securely and not exposed in logs
- All API calls use HTTPS
- Dashboard requires server access (no public exposure)
- Input validation on all endpoints

## Development

### File Structure

```
src/
├── services/
│   └── RenderApiService.ts     # Main Render API integration
├── routes/
│   └── render.ts               # API endpoints
└── app.ts                      # Route registration

public/
└── render-dashboard.html       # Dashboard interface

test-render-service.js          # Integration tests
```

### Adding New Features

To extend the Render integration:

1. Add new methods to `RenderApiService.ts`
2. Create corresponding routes in `render.ts`
3. Update the dashboard HTML if needed
4. Add tests to `test-render-service.js`

## Support

For issues with:
- **Render API**: Check [Render Documentation](https://render.com/docs/api)
- **Integration**: Review the logs and test script output
- **Dashboard**: Check browser console for errors

## License

This integration is part of the Shopify Return Automation system and follows the same license terms.
